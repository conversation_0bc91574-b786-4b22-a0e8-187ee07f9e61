import React from 'react'
import { useParams } from 'react-router-dom'
import { TrendingUp, Plus, Bell } from 'lucide-react'

export const StockDetail: React.FC = () => {
  const { symbol } = useParams<{ symbol: string }>()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{symbol?.toUpperCase()}</h1>
          <p className="text-gray-600 mt-1">Stock Details</p>
        </div>
        <div className="flex space-x-3">
          <button className="btn-outline">
            <Bell className="h-4 w-4 mr-2" />
            Set Alert
          </button>
          <button className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Add to Watchlist
          </button>
        </div>
      </div>

      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Stock details for {symbol?.toUpperCase()}</p>
            <p className="text-sm text-gray-500 mt-1">
              Chart and detailed information would be displayed here
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
