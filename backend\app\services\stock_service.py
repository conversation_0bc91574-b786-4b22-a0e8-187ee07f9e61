"""
Stock service for business logic related to stock operations.
"""
import asyncio
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime, timedelta

from ..models.stock import Stock, StockPriceHistory
from ..schemas.stock import (
    StockCreate, StockUpdate, StockResponse, StockScreeningCriteria,
    StockScreeningResult, StockScreeningMatch, StockSearchResult, StockHistory,
    EntryPointAnalysis, EntrySignal, EntryRecommendation
)
from ..services.market_data_service import market_data_service
from ..algorithms.technical_indicators import calculate_all_indicators, generate_trading_signals
from ..algorithms.entry_point_detector import detect_entry_point
from ..algorithms.screening_algorithms import (
    screen_momentum_stocks, screen_oversold_stocks, screen_breakout_stocks
)
from ..utils.logger import get_logger, log_screening_result
from ..core.exceptions import StockNotFoundError, ValidationError

logger = get_logger(__name__)


class StockService:
    """Service class for stock-related operations."""
    
    def __init__(self):
        self.market_data_service = market_data_service
    
    async def get_stock_by_symbol(self, db: Session, symbol: str) -> Optional[Stock]:
        """Get stock by symbol."""
        try:
            return db.query(Stock).filter(
                Stock.symbol == symbol.upper(),
                Stock.is_active == True
            ).first()
        except Exception as e:
            logger.error(f"Error getting stock by symbol {symbol}: {e}")
            return None
    
    async def get_stock_by_id(self, db: Session, stock_id: int) -> Optional[Stock]:
        """Get stock by ID."""
        try:
            return db.query(Stock).filter(
                Stock.id == stock_id,
                Stock.is_active == True
            ).first()
        except Exception as e:
            logger.error(f"Error getting stock by ID {stock_id}: {e}")
            return None
    
    async def create_stock(self, db: Session, stock_data: StockCreate) -> Stock:
        """Create a new stock."""
        try:
            # Check if stock already exists
            existing_stock = await self.get_stock_by_symbol(db, stock_data.symbol)
            if existing_stock:
                raise ValidationError(f"Stock with symbol {stock_data.symbol} already exists")
            
            # Create new stock
            stock = Stock(
                symbol=stock_data.symbol.upper(),
                name=stock_data.name,
                exchange=stock_data.exchange,
                sector=stock_data.sector,
                industry=stock_data.industry,
                market_cap=stock_data.market_cap,
                shares_outstanding=stock_data.shares_outstanding
            )
            
            db.add(stock)
            db.commit()
            db.refresh(stock)
            
            logger.info(f"Created new stock: {stock.symbol}")
            return stock
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating stock: {e}")
            raise
    
    async def update_stock(self, db: Session, stock_id: int, stock_data: StockUpdate) -> Optional[Stock]:
        """Update stock information."""
        try:
            stock = await self.get_stock_by_id(db, stock_id)
            if not stock:
                raise StockNotFoundError(f"Stock with ID {stock_id} not found")
            
            # Update fields
            update_data = stock_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(stock, field, value)
            
            stock.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(stock)
            
            logger.info(f"Updated stock: {stock.symbol}")
            return stock
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating stock {stock_id}: {e}")
            raise
    
    async def get_stocks(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        sector: Optional[str] = None,
        exchange: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[Stock]:
        """Get list of stocks with filtering."""
        try:
            query = db.query(Stock).filter(Stock.is_active == True)
            
            if sector:
                query = query.filter(Stock.sector == sector)
            
            if exchange:
                query = query.filter(Stock.exchange == exchange)
            
            if search:
                search_term = f"%{search}%"
                query = query.filter(
                    or_(
                        Stock.symbol.ilike(search_term),
                        Stock.name.ilike(search_term)
                    )
                )
            
            return query.offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error getting stocks: {e}")
            return []
    
    async def update_stock_price_data(self, db: Session, stock: Stock) -> bool:
        """Update stock with latest price data."""
        try:
            quote_data = await self.market_data_service.get_stock_quote(stock.symbol)
            if not quote_data:
                return False
            
            # Update stock with latest data
            stock.current_price = quote_data.get('current_price')
            stock.previous_close = quote_data.get('previous_close')
            stock.open_price = quote_data.get('open_price')
            stock.day_high = quote_data.get('day_high')
            stock.day_low = quote_data.get('day_low')
            stock.volume = quote_data.get('volume')
            stock.price_change = quote_data.get('price_change')
            stock.price_change_percent = quote_data.get('price_change_percent')
            stock.last_data_update = datetime.utcnow()
            
            # Calculate technical indicators if we have price data
            if stock.current_price:
                try:
                    # Get historical data for technical indicators
                    historical_data = await self.get_stock_history(
                        db, stock.symbol, "1d", 200
                    )
                    if historical_data and len(historical_data.data) > 20:
                        prices = [point.close for point in historical_data.data]
                        volumes = [point.volume for point in historical_data.data]
                        
                        indicators = calculate_all_indicators(prices, volumes)
                        stock.technical_indicators = indicators
                        
                except Exception as e:
                    logger.warning(f"Error calculating technical indicators for {stock.symbol}: {e}")
            
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error updating stock price data for {stock.symbol}: {e}")
            db.rollback()
            return False
    
    async def get_stock_history(
        self,
        db: Session,
        symbol: str,
        timeframe: str = "1d",
        limit: int = 100
    ) -> Optional[StockHistory]:
        """Get historical price data for a stock."""
        try:
            # First try to get from database
            stock = await self.get_stock_by_symbol(db, symbol)
            if not stock:
                return None
            
            # Get historical data from market data service
            historical_data = await self.market_data_service.get_historical_data(
                symbol, timeframe, limit
            )
            
            if historical_data:
                return StockHistory(
                    symbol=symbol,
                    timeframe=timeframe,
                    data=historical_data
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting stock history for {symbol}: {e}")
            return None
    
    async def screen_stocks(
        self,
        db: Session,
        criteria: StockScreeningCriteria
    ) -> StockScreeningResult:
        """Enhanced stock screening with technical analysis."""
        start_time = datetime.utcnow()

        try:
            # Start with basic database filtering
            query = db.query(Stock).filter(
                Stock.is_active == True,
                Stock.is_tradable == True
            )

            # Apply basic filters
            if criteria.min_price is not None:
                query = query.filter(Stock.current_price >= criteria.min_price)
            if criteria.max_price is not None:
                query = query.filter(Stock.current_price <= criteria.max_price)
            if criteria.min_volume is not None:
                query = query.filter(Stock.volume >= criteria.min_volume)
            if criteria.min_market_cap is not None:
                query = query.filter(Stock.market_cap >= criteria.min_market_cap)
            if criteria.max_market_cap is not None:
                query = query.filter(Stock.market_cap <= criteria.max_market_cap)
            if criteria.sectors:
                query = query.filter(Stock.sector.in_(criteria.sectors))
            if criteria.exchanges:
                query = query.filter(Stock.exchange.in_(criteria.exchanges))

            # Get initial results
            initial_results = query.limit(1000).all()

            # Apply advanced technical analysis filtering
            enhanced_results = []
            signal_counts = {}
            total_technical_score = 0

            for stock in initial_results:
                try:
                    # Analyze technical indicators
                    technical_analysis = await self._analyze_stock_technical(stock, criteria)

                    if technical_analysis and technical_analysis['passes_criteria']:
                        enhanced_results.append(technical_analysis)
                        total_technical_score += technical_analysis['technical_score']

                        # Count signals for summary
                        for signal in technical_analysis['signals']:
                            signal_counts[signal] = signal_counts.get(signal, 0) + 1

                except Exception as e:
                    logger.error(f"Error analyzing stock {stock.symbol}: {e}")
                    continue

            # Sort by technical score (highest first)
            enhanced_results.sort(key=lambda x: x['technical_score'], reverse=True)

            # Calculate summary statistics
            avg_score = total_technical_score / len(enhanced_results) if enhanced_results else 0
            top_signals = sorted(signal_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            top_signals = [signal for signal, count in top_signals]
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            # Create enhanced screening matches
            screening_matches = []
            for result in enhanced_results:
                screening_matches.append(StockScreeningMatch(
                    stock=StockResponse.from_orm(result['stock']),
                    technical_score=result['technical_score'],
                    signals=result['signals'],
                    entry_recommendation=result['entry_recommendation'],
                    confidence=result['confidence'],
                    rsi=result.get('rsi'),
                    macd_signal=result.get('macd_signal'),
                    volume_surge=result.get('volume_surge'),
                    price_momentum=result.get('price_momentum')
                ))

            # Log screening result
            log_screening_result(
                criteria.dict(),
                len(screening_matches),
                execution_time
            )

            return StockScreeningResult(
                criteria=criteria,
                results=screening_matches,
                total_matches=len(screening_matches),
                execution_time=execution_time,
                timestamp=datetime.utcnow(),
                avg_technical_score=avg_score,
                top_signals=top_signals,
                screening_summary={
                    "initial_matches": len(initial_results),
                    "technical_filtered": len(enhanced_results),
                    "filter_efficiency": len(enhanced_results) / len(initial_results) if initial_results else 0,
                    "avg_confidence": sum(r['confidence'] for r in enhanced_results) / len(enhanced_results) if enhanced_results else 0
                }
            )

        except Exception as e:
            logger.error(f"Error screening stocks: {e}")
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            return StockScreeningResult(
                criteria=criteria,
                results=[],
                total_matches=0,
                execution_time=execution_time,
                timestamp=datetime.utcnow(),
                avg_technical_score=0,
                top_signals=[],
                screening_summary={"error": str(e)}
            )
    
    async def search_stocks(self, query: str, limit: int = 10) -> List[StockSearchResult]:
        """Search for stocks by symbol or name."""
        try:
            # Use market data service for search
            search_results = await self.market_data_service.search_stocks(query, limit)
            
            return [
                StockSearchResult(
                    symbol=result['symbol'],
                    name=result['name'],
                    exchange=result['exchange'],
                    type=result['type'],
                    currency=result.get('currency', 'USD')
                )
                for result in search_results
            ]
            
        except Exception as e:
            logger.error(f"Error searching stocks for query '{query}': {e}")
            return []

    async def _analyze_stock_technical(self, stock: Stock, criteria: StockScreeningCriteria) -> Optional[Dict[str, Any]]:
        """Analyze stock using technical indicators and criteria."""
        try:
            # Check if stock has technical indicators
            if not stock.technical_indicators or 'indicators' not in stock.technical_indicators:
                return None

            indicators = stock.technical_indicators['indicators']
            signals = stock.technical_indicators.get('signals', {})

            # Initialize analysis result
            analysis = {
                'stock': stock,
                'technical_score': 0,
                'signals': [],
                'entry_recommendation': 'HOLD',
                'confidence': 0.0,
                'passes_criteria': True
            }

            # Check RSI criteria
            rsi_value = indicators.get('rsi')
            if rsi_value is not None:
                analysis['rsi'] = rsi_value

                # Apply RSI filters
                if criteria.rsi_min is not None and rsi_value < criteria.rsi_min:
                    analysis['passes_criteria'] = False
                if criteria.rsi_max is not None and rsi_value > criteria.rsi_max:
                    analysis['passes_criteria'] = False

                # RSI signals
                if rsi_value < 30:
                    analysis['signals'].append('RSI Oversold')
                    analysis['technical_score'] += 20
                elif rsi_value > 70:
                    analysis['signals'].append('RSI Overbought')
                    analysis['technical_score'] += 10

            # Check MACD
            macd_data = indicators.get('macd', {})
            if isinstance(macd_data, dict):
                macd_line = macd_data.get('macd')
                signal_line = macd_data.get('signal')

                if macd_line is not None and signal_line is not None:
                    if macd_line > signal_line:
                        analysis['macd_signal'] = 'bullish'
                        analysis['signals'].append('MACD Bullish')
                        analysis['technical_score'] += 15
                    else:
                        analysis['macd_signal'] = 'bearish'
                        analysis['signals'].append('MACD Bearish')
                        analysis['technical_score'] += 5

            # Check volume surge
            if criteria.volume_surge_threshold and stock.volume and stock.avg_volume:
                volume_ratio = stock.volume / stock.avg_volume
                if volume_ratio >= criteria.volume_surge_threshold:
                    analysis['volume_surge'] = True
                    analysis['signals'].append('Volume Surge')
                    analysis['technical_score'] += 25
                else:
                    analysis['volume_surge'] = False

            # Check price momentum
            if stock.price_change_percent:
                if stock.price_change_percent > 2:
                    analysis['price_momentum'] = 'up'
                    analysis['signals'].append('Strong Upward Momentum')
                    analysis['technical_score'] += 20
                elif stock.price_change_percent > 0:
                    analysis['price_momentum'] = 'up'
                    analysis['signals'].append('Upward Momentum')
                    analysis['technical_score'] += 10
                elif stock.price_change_percent < -2:
                    analysis['price_momentum'] = 'down'
                    analysis['signals'].append('Strong Downward Momentum')
                    analysis['technical_score'] += 5
                else:
                    analysis['price_momentum'] = 'neutral'

            # Generate entry recommendation based on signals
            if analysis['technical_score'] >= 60:
                analysis['entry_recommendation'] = 'STRONG_BUY'
                analysis['confidence'] = 0.8
            elif analysis['technical_score'] >= 40:
                analysis['entry_recommendation'] = 'BUY'
                analysis['confidence'] = 0.6
            elif analysis['technical_score'] >= 20:
                analysis['entry_recommendation'] = 'WATCH'
                analysis['confidence'] = 0.4
            else:
                analysis['entry_recommendation'] = 'HOLD'
                analysis['confidence'] = 0.2

            return analysis if analysis['passes_criteria'] else None

        except Exception as e:
            logger.error(f"Error in technical analysis for {stock.symbol}: {e}")
            return None

    async def analyze_entry_point(self, db: Session, symbol: str) -> Optional[EntryPointAnalysis]:
        """Analyze entry point for a specific stock."""
        try:
            # Verify stock exists
            stock = await self.get_stock_by_symbol(db, symbol)
            if not stock:
                raise StockNotFoundError(f"Stock with symbol {symbol} not found")

            # Get historical data for analysis
            historical_data = await self.market_data_service.get_historical_data(
                symbol, period="3mo"
            )

            if not historical_data or len(historical_data) < 50:
                logger.warning(f"Insufficient historical data for {symbol}")
                return None

            # Perform entry point analysis
            analysis_result = detect_entry_point(historical_data)

            if "error" in analysis_result:
                logger.error(f"Entry point analysis error for {symbol}: {analysis_result['error']}")
                return None

            # Convert to response schema
            entry_signals = {}
            for signal_name, signal_data in analysis_result["entry_signals"].items():
                entry_signals[signal_name] = EntrySignal(
                    detected=signal_data.get("detected", False),
                    confidence=signal_data.get("confidence", 0.0),
                    description=signal_data.get("description"),
                    current_rsi=signal_data.get("current_rsi"),
                    resistance_level=signal_data.get("resistance_level"),
                    support_level=signal_data.get("support_level"),
                    volume_ratio=signal_data.get("volume_ratio"),
                    sma_20=signal_data.get("sma_20"),
                    sma_50=signal_data.get("sma_50"),
                    current_width=signal_data.get("current_width"),
                    avg_width=signal_data.get("avg_width"),
                    recent_low=signal_data.get("recent_low")
                )

            recommendation_data = analysis_result["recommendation"]
            recommendation = EntryRecommendation(
                action=recommendation_data["action"],
                reason=recommendation_data["reason"],
                entry_score=recommendation_data["entry_score"],
                active_signals=recommendation_data["active_signals"],
                signal_count=recommendation_data["signal_count"]
            )

            return EntryPointAnalysis(
                symbol=symbol.upper(),
                current_price=analysis_result["current_price"],
                analysis_date=datetime.fromisoformat(analysis_result["analysis_date"].replace('Z', '+00:00')),
                entry_signals=entry_signals,
                entry_score=analysis_result["entry_score"],
                recommendation=recommendation,
                indicators=analysis_result["indicators"]
            )

        except Exception as e:
            logger.error(f"Error analyzing entry point for {symbol}: {e}")
            return None


# Global service instance
stock_service = StockService()
