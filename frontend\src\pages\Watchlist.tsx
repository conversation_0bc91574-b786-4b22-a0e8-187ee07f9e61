import React from 'react'
import { Plus, Edit, Trash2, Star } from 'lucide-react'

export const Watchlist: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Watchlists</h1>
          <p className="text-gray-600 mt-1">
            Organize and track your favorite stocks
          </p>
        </div>
        <button className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          New Watchlist
        </button>
      </div>

      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No watchlists yet</p>
            <p className="text-sm text-gray-500 mt-1">
              Create your first watchlist to start tracking stocks
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
