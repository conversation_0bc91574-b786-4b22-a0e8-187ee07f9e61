"""
Pydantic schemas for user-related API endpoints.
"""
from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from datetime import datetime


class UserBase(BaseModel):
    """Base user schema with common fields."""
    email: EmailStr
    username: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    
    @validator('username')
    def username_alphanumeric(cls, v):
        assert v.isalnum(), 'Username must be alphanumeric'
        assert len(v) >= 3, 'Username must be at least 3 characters'
        assert len(v) <= 50, 'Username must be less than 50 characters'
        return v


class UserCreate(UserBase):
    """Schema for user registration."""
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        assert len(v) >= 8, 'Password must be at least 8 characters'
        assert any(c.isupper() for c in v), 'Password must contain uppercase letter'
        assert any(c.islower() for c in v), 'Password must contain lowercase letter'
        assert any(c.isdigit() for c in v), 'Password must contain digit'
        return v


class UserUpdate(BaseModel):
    """Schema for user profile updates."""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    bio: Optional[str] = None
    timezone: Optional[str] = None
    notification_email: Optional[bool] = None
    notification_push: Optional[bool] = None


class UserResponse(UserBase):
    """Schema for user response data."""
    id: int
    full_name: str
    is_active: bool
    is_verified: bool
    is_premium: bool
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    timezone: str
    notification_email: bool
    notification_push: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """Schema for user login."""
    username: str
    password: str


class UserPasswordChange(BaseModel):
    """Schema for password change."""
    current_password: str
    new_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        assert len(v) >= 8, 'Password must be at least 8 characters'
        assert any(c.isupper() for c in v), 'Password must contain uppercase letter'
        assert any(c.islower() for c in v), 'Password must contain lowercase letter'
        assert any(c.isdigit() for c in v), 'Password must contain digit'
        return v


class Token(BaseModel):
    """Schema for JWT token response."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class TokenData(BaseModel):
    """Schema for token data."""
    username: Optional[str] = None
    user_id: Optional[int] = None
