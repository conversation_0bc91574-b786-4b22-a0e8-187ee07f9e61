import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { useAuthStore } from '@/store/authStore'

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    // Handle different error types
    if (error.response) {
      // Server responded with error status
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 401:
          // Token expired or invalid, logout user
          useAuthStore.getState().logout()
          window.location.href = '/login'
          break
        case 403:
          // Forbidden - user doesn't have permission
          console.error('Access denied:', data.error || 'Forbidden')
          break
        case 404:
          // Resource not found
          console.error('Resource not found:', data.error || 'Not found')
          break
        case 422:
          // Validation error
          console.error('Validation error:', data.error || 'Invalid input')
          break
        case 429:
          // Rate limit exceeded
          console.error('Rate limit exceeded. Please try again later.')
          break
        case 500:
          // Server error
          console.error('Server error:', data.error || 'Internal server error')
          break
        default:
          console.error('API error:', data.error || `HTTP ${status}`)
      }

      // Enhance error object with structured data
      error.apiError = {
        status,
        message: data.error || `HTTP ${status}`,
        details: data.details || {},
        timestamp: data.timestamp,
        path: data.path
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message)
      error.apiError = {
        status: 0,
        message: 'Network error. Please check your connection.',
        details: { originalError: error.message }
      }
    } else {
      // Other error
      console.error('Request error:', error.message)
      error.apiError = {
        status: 0,
        message: 'Request failed',
        details: { originalError: error.message }
      }
    }

    return Promise.reject(error)
  }
)

// Generic API methods
export const apiClient = {
  get: <T>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.get(url, config).then((response) => response.data),
    
  post: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.post(url, data, config).then((response) => response.data),
    
  put: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.put(url, data, config).then((response) => response.data),
    
  patch: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.patch(url, data, config).then((response) => response.data),
    
  delete: <T>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.delete(url, config).then((response) => response.data),
}

export default api
