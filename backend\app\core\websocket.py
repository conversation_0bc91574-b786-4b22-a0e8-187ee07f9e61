"""
WebSocket manager for real-time updates.
"""
from typing import Dict, List
from fastapi import WebSocket, WebSocketDisconnect
import json
import asyncio
from datetime import datetime

from ..utils.logger import get_logger

logger = get_logger(__name__)


class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        # Store active connections by client_id
        self.active_connections: Dict[str, WebSocket] = {}
        # Store user subscriptions (user_id -> list of topics)
        self.user_subscriptions: Dict[int, List[str]] = {}
        # Store topic subscribers (topic -> list of client_ids)
        self.topic_subscribers: Dict[str, List[str]] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket connection established for client: {client_id}")
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection."""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            
            # Remove from all topic subscriptions
            for topic, subscribers in self.topic_subscribers.items():
                if client_id in subscribers:
                    subscribers.remove(client_id)
            
            logger.info(f"WebSocket connection closed for client: {client_id}")
    
    async def send_personal_message(self, message: str, client_id: str):
        """Send a message to a specific client."""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(message)
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)
    
    async def send_json_message(self, data: dict, client_id: str):
        """Send JSON data to a specific client."""
        try:
            message = json.dumps(data)
            await self.send_personal_message(message, client_id)
        except Exception as e:
            logger.error(f"Error sending JSON message to {client_id}: {e}")
    
    async def broadcast_to_topic(self, message: str, topic: str):
        """Broadcast a message to all subscribers of a topic."""
        if topic in self.topic_subscribers:
            disconnected_clients = []
            
            for client_id in self.topic_subscribers[topic]:
                try:
                    await self.send_personal_message(message, client_id)
                except Exception as e:
                    logger.error(f"Error broadcasting to {client_id}: {e}")
                    disconnected_clients.append(client_id)
            
            # Clean up disconnected clients
            for client_id in disconnected_clients:
                self.disconnect(client_id)
    
    async def broadcast_json_to_topic(self, data: dict, topic: str):
        """Broadcast JSON data to all subscribers of a topic."""
        try:
            message = json.dumps(data)
            await self.broadcast_to_topic(message, topic)
        except Exception as e:
            logger.error(f"Error broadcasting JSON to topic {topic}: {e}")
    
    def subscribe_to_topic(self, client_id: str, topic: str):
        """Subscribe a client to a topic."""
        if topic not in self.topic_subscribers:
            self.topic_subscribers[topic] = []
        
        if client_id not in self.topic_subscribers[topic]:
            self.topic_subscribers[topic].append(client_id)
            logger.info(f"Client {client_id} subscribed to topic: {topic}")
    
    def unsubscribe_from_topic(self, client_id: str, topic: str):
        """Unsubscribe a client from a topic."""
        if topic in self.topic_subscribers and client_id in self.topic_subscribers[topic]:
            self.topic_subscribers[topic].remove(client_id)
            logger.info(f"Client {client_id} unsubscribed from topic: {topic}")
    
    async def send_stock_update(self, stock_data: dict):
        """Send stock price update to subscribers."""
        symbol = stock_data.get("symbol")
        if symbol:
            topic = f"stock:{symbol}"
            message_data = {
                "type": "stock_update",
                "data": stock_data,
                "timestamp": datetime.utcnow().isoformat()
            }
            await self.broadcast_json_to_topic(message_data, topic)
    
    async def send_alert_notification(self, alert_data: dict, user_id: int):
        """Send alert notification to a specific user."""
        # Find client connections for this user
        user_clients = [
            client_id for client_id, ws in self.active_connections.items()
            if client_id.startswith(f"user_{user_id}_")
        ]
        
        message_data = {
            "type": "alert",
            "data": alert_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        for client_id in user_clients:
            await self.send_json_message(message_data, client_id)
    
    async def send_watchlist_update(self, watchlist_data: dict, user_id: int):
        """Send watchlist update to a specific user."""
        user_clients = [
            client_id for client_id, ws in self.active_connections.items()
            if client_id.startswith(f"user_{user_id}_")
        ]
        
        message_data = {
            "type": "watchlist_update",
            "data": watchlist_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        for client_id in user_clients:
            await self.send_json_message(message_data, client_id)
    
    async def send_screening_results(self, results: list, client_id: str):
        """Send screening results to a specific client."""
        message_data = {
            "type": "screening_results",
            "data": results,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_json_message(message_data, client_id)
    
    def get_connection_stats(self) -> dict:
        """Get connection statistics."""
        return {
            "active_connections": len(self.active_connections),
            "topics": len(self.topic_subscribers),
            "total_subscriptions": sum(len(subs) for subs in self.topic_subscribers.values())
        }


# Global WebSocket manager instance
websocket_manager = ConnectionManager()


async def handle_websocket_message(client_id: str, message: str):
    """Handle incoming WebSocket messages."""
    try:
        data = json.loads(message)
        message_type = data.get("type")
        
        if message_type == "subscribe":
            topic = data.get("topic")
            if topic:
                websocket_manager.subscribe_to_topic(client_id, topic)
        
        elif message_type == "unsubscribe":
            topic = data.get("topic")
            if topic:
                websocket_manager.unsubscribe_from_topic(client_id, topic)
        
        elif message_type == "ping":
            # Respond to ping with pong
            await websocket_manager.send_json_message({"type": "pong"}, client_id)
        
        else:
            logger.warning(f"Unknown message type: {message_type}")
    
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON message from {client_id}: {message}")
    except Exception as e:
        logger.error(f"Error handling WebSocket message from {client_id}: {e}")
