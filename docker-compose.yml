version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: entryalert_postgres
    environment:
      POSTGRES_DB: entryalert
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - entryalert_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: entryalert_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - entryalert_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: entryalert_backend
    environment:
      - DATABASE_URL=********************************************/entryalert
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-change-in-production
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - Y<PERSON><PERSON><PERSON><PERSON>_FINANCE_API_KEY=${YAHOO_FINANCE_API_KEY}
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - entryalert_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: entryalert_frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - entryalert_network
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000

  # Background Worker (for scheduled tasks)
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: entryalert_worker
    environment:
      - DATABASE_URL=********************************************/entryalert
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-change-in-production
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - YAHOO_FINANCE_API_KEY=${YAHOO_FINANCE_API_KEY}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - entryalert_network
    command: python -m app.core.background_tasks

volumes:
  postgres_data:
  redis_data:

networks:
  entryalert_network:
    driver: bridge
