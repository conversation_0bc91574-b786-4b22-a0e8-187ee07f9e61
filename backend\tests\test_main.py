"""
Tests for the main FastAPI application.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.database import get_db, Base
from app.config import settings

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create tables
Base.metadata.create_all(bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


def test_health_check():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["app_name"] == settings.APP_NAME


def test_root_endpoint():
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert settings.APP_NAME in data["message"]


def test_register_user():
    """Test user registration."""
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "TestPassword123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 201
    data = response.json()
    assert data["email"] == user_data["email"]
    assert data["username"] == user_data["username"]
    assert "id" in data


def test_login_user():
    """Test user login."""
    # First register a user
    user_data = {
        "email": "<EMAIL>",
        "username": "loginuser",
        "password": "TestPassword123"
    }
    client.post("/api/v1/auth/register", json=user_data)
    
    # Then login
    login_data = {
        "username": "loginuser",
        "password": "TestPassword123"
    }
    
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
    assert "user" in data


def test_protected_route_without_token():
    """Test accessing protected route without token."""
    response = client.get("/api/v1/auth/me")
    assert response.status_code == 401


def test_protected_route_with_token():
    """Test accessing protected route with valid token."""
    # Register and login to get token
    user_data = {
        "email": "<EMAIL>",
        "username": "protecteduser",
        "password": "TestPassword123"
    }
    client.post("/api/v1/auth/register", json=user_data)
    
    login_data = {
        "username": "protecteduser",
        "password": "TestPassword123"
    }
    
    login_response = client.post("/api/v1/auth/login", data=login_data)
    token = login_response.json()["access_token"]
    
    # Access protected route
    headers = {"Authorization": f"Bearer {token}"}
    response = client.get("/api/v1/auth/me", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "protecteduser"


def test_get_stocks():
    """Test getting stocks list."""
    # Register and login to get token
    user_data = {
        "email": "<EMAIL>",
        "username": "stocksuser",
        "password": "TestPassword123"
    }
    client.post("/api/v1/auth/register", json=user_data)
    
    login_data = {
        "username": "stocksuser",
        "password": "TestPassword123"
    }
    
    login_response = client.post("/api/v1/auth/login", data=login_data)
    token = login_response.json()["access_token"]
    
    # Get stocks
    headers = {"Authorization": f"Bearer {token}"}
    response = client.get("/api/v1/stocks/", headers=headers)
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_invalid_login():
    """Test login with invalid credentials."""
    login_data = {
        "username": "nonexistent",
        "password": "wrongpassword"
    }
    
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 401


def test_duplicate_user_registration():
    """Test registering user with duplicate email/username."""
    user_data = {
        "email": "<EMAIL>",
        "username": "duplicateuser",
        "password": "TestPassword123"
    }
    
    # First registration should succeed
    response1 = client.post("/api/v1/auth/register", json=user_data)
    assert response1.status_code == 201
    
    # Second registration should fail
    response2 = client.post("/api/v1/auth/register", json=user_data)
    assert response2.status_code == 400
