#!/usr/bin/env python3
"""
Management script for EntryAlert application.
Provides commands for database operations, seeding, and maintenance.
"""
import asyncio
import sys
import argparse
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database import DatabaseManager, engine, Base
from app.scripts.seed_stocks import StockDataSeeder
from app.utils.logger import get_logger

logger = get_logger(__name__)


async def init_database():
    """Initialize database tables."""
    try:
        logger.info("Initializing database...")
        
        # Import all models to ensure they are registered
        from app.models import user, stock, watchlist, alert
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully!")
        
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        sys.exit(1)


async def reset_database():
    """Reset database by dropping and recreating all tables."""
    try:
        logger.info("Resetting database...")
        
        # Import all models
        from app.models import user, stock, watchlist, alert
        
        # Drop and recreate tables
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        
        logger.info("Database reset successfully!")
        
    except Exception as e:
        logger.error(f"Error resetting database: {e}")
        sys.exit(1)


async def seed_stocks():
    """Seed stock data."""
    try:
        logger.info("Starting stock data seeding...")
        seeder = StockDataSeeder()
        await seeder.seed_major_stocks()
        logger.info("Stock seeding completed!")
        
    except Exception as e:
        logger.error(f"Error seeding stocks: {e}")
        sys.exit(1)


async def update_prices():
    """Update stock prices."""
    try:
        logger.info("Starting price updates...")
        seeder = StockDataSeeder()
        await seeder.update_stock_prices()
        logger.info("Price updates completed!")
        
    except Exception as e:
        logger.error(f"Error updating prices: {e}")
        sys.exit(1)


async def full_setup():
    """Full setup: initialize database and seed stocks."""
    try:
        logger.info("Starting full setup...")
        
        # Initialize database
        await init_database()
        
        # Seed stocks
        await seed_stocks()
        
        logger.info("Full setup completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in full setup: {e}")
        sys.exit(1)


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(description='EntryAlert Management Script')
    parser.add_argument('command', choices=[
        'init-db', 'reset-db', 'seed-stocks', 'update-prices', 'full-setup'
    ], help='Command to execute')
    
    args = parser.parse_args()
    
    # Map commands to functions
    commands = {
        'init-db': init_database,
        'reset-db': reset_database,
        'seed-stocks': seed_stocks,
        'update-prices': update_prices,
        'full-setup': full_setup
    }
    
    # Execute the command
    command_func = commands[args.command]
    asyncio.run(command_func())


if __name__ == "__main__":
    main()
