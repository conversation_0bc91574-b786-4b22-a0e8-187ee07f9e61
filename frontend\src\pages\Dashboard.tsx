import React from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Activity,
  Bell,
  Eye,
  Plus
} from 'lucide-react'
import { useAuthStore } from '@/store/authStore'

// Mock data for demonstration
const marketStats = [
  {
    name: 'S&P 500',
    value: '4,567.89',
    change: '+23.45',
    changePercent: '+0.52%',
    isPositive: true,
  },
  {
    name: 'NASDAQ',
    value: '14,234.56',
    change: '-45.67',
    changePercent: '-0.32%',
    isPositive: false,
  },
  {
    name: 'DOW',
    value: '34,567.12',
    change: '+123.45',
    changePercent: '+0.36%',
    isPositive: true,
  },
]

const watchlistStocks = [
  {
    symbol: 'AAPL',
    name: 'Apple Inc.',
    price: 175.43,
    change: 2.34,
    changePercent: 1.35,
    volume: '45.2M',
  },
  {
    symbol: 'GOOGL',
    name: 'Alphabet Inc.',
    price: 2834.56,
    change: -12.45,
    changePercent: -0.44,
    volume: '23.1M',
  },
  {
    symbol: 'MSFT',
    name: 'Microsoft Corp.',
    price: 334.78,
    change: 5.67,
    changePercent: 1.72,
    volume: '32.8M',
  },
]

const recentAlerts = [
  {
    id: 1,
    stock: 'TSLA',
    message: 'Price crossed above $200',
    time: '2 minutes ago',
    type: 'price',
  },
  {
    id: 2,
    stock: 'NVDA',
    message: 'Volume surge detected',
    time: '15 minutes ago',
    type: 'volume',
  },
  {
    id: 3,
    stock: 'AMD',
    message: 'RSI oversold condition',
    time: '1 hour ago',
    type: 'technical',
  },
]

export const Dashboard: React.FC = () => {
  const { user } = useAuthStore()

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.first_name || user?.username}!
            </h1>
            <p className="text-gray-600 mt-1">
              Here's what's happening in the markets today
            </p>
          </div>
          <div className="flex space-x-3">
            <button className="btn-outline">
              <Eye className="h-4 w-4 mr-2" />
              View All
            </button>
            <button className="btn-primary">
              <Plus className="h-4 w-4 mr-2" />
              Add Stock
            </button>
          </div>
        </div>
      </div>

      {/* Market Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {marketStats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className="flex items-center">
                  {stat.isPositive ? (
                    <TrendingUp className="h-5 w-5 text-success-500" />
                  ) : (
                    <TrendingDown className="h-5 w-5 text-danger-500" />
                  )}
                </div>
              </div>
              <div className="mt-2 flex items-center">
                <span
                  className={`text-sm font-medium ${
                    stat.isPositive ? 'text-success-600' : 'text-danger-600'
                  }`}
                >
                  {stat.change} ({stat.changePercent})
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Watchlist */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">My Watchlist</h3>
              <button className="text-primary-600 hover:text-primary-500 text-sm font-medium">
                View All
              </button>
            </div>
          </div>
          <div className="card-body p-0">
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Stock
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Change
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Volume
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {watchlistStocks.map((stock) => (
                    <tr key={stock.symbol} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {stock.symbol}
                          </div>
                          <div className="text-sm text-gray-500">{stock.name}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${stock.price.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`text-sm font-medium ${
                            stock.change >= 0 ? 'text-success-600' : 'text-danger-600'
                          }`}
                        >
                          {stock.change >= 0 ? '+' : ''}
                          {stock.change.toFixed(2)} ({stock.changePercent.toFixed(2)}%)
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {stock.volume}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Recent Alerts */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Recent Alerts</h3>
              <button className="text-primary-600 hover:text-primary-500 text-sm font-medium">
                View All
              </button>
            </div>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {recentAlerts.map((alert) => (
                <div key={alert.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <Bell className="h-4 w-4 text-primary-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {alert.stock}
                    </p>
                    <p className="text-sm text-gray-500">{alert.message}</p>
                    <p className="text-xs text-gray-400 mt-1">{alert.time}</p>
                  </div>
                  <div className="flex-shrink-0">
                    <span className="badge badge-primary">{alert.type}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-success-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Portfolio Value</p>
                <p className="text-xl font-bold text-gray-900">$12,345</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Activity className="h-8 w-8 text-primary-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                <p className="text-xl font-bold text-gray-900">8</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Eye className="h-8 w-8 text-warning-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Watchlist Items</p>
                <p className="text-xl font-bold text-gray-900">24</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-success-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today's Gainers</p>
                <p className="text-xl font-bold text-gray-900">156</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
