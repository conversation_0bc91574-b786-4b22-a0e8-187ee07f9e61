import React from 'react'
import {
  ComposedChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell
} from 'recharts'
import { format } from 'date-fns'
import { ChartDataPoint } from '@/types'

interface CandlestickChartProps {
  data: ChartDataPoint[]
  height?: number
  showGrid?: boolean
  showTooltip?: boolean
  showVolume?: boolean
}

export const CandlestickChart: React.FC<CandlestickChartProps> = ({
  data,
  height = 400,
  showGrid = true,
  showTooltip = true,
  showVolume = true
}) => {
  // Transform data for candlestick representation
  const transformedData = data.map((item) => {
    const open = item.open || item.y
    const close = item.close || item.y
    const high = item.high || item.y
    const low = item.low || item.y
    
    return {
      ...item,
      open,
      close,
      high,
      low,
      bodyHeight: Math.abs(close - open),
      bodyY: Math.min(open, close),
      wickHeight: high - low,
      wickY: low,
      isGreen: close >= open,
      volume: item.volume || 0
    }
  })

  const formatXAxisTick = (tickItem: any) => {
    if (typeof tickItem === 'string') {
      try {
        return format(new Date(tickItem), 'MMM dd')
      } catch {
        return tickItem
      }
    }
    return tickItem
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">
            {typeof label === 'string' ? format(new Date(label), 'MMM dd, yyyy') : label}
          </p>
          <div className="mt-2 space-y-1">
            <div className="flex justify-between gap-4">
              <span className="text-xs text-gray-600">Open:</span>
              <span className="text-xs font-medium">${data.open?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-xs text-gray-600">High:</span>
              <span className="text-xs font-medium text-green-600">${data.high?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-xs text-gray-600">Low:</span>
              <span className="text-xs font-medium text-red-600">${data.low?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-xs text-gray-600">Close:</span>
              <span className={`text-xs font-medium ${data.isGreen ? 'text-green-600' : 'text-red-600'}`}>
                ${data.close?.toFixed(2)}
              </span>
            </div>
            {data.volume > 0 && (
              <div className="flex justify-between gap-4 pt-1 border-t border-gray-100">
                <span className="text-xs text-gray-600">Volume:</span>
                <span className="text-xs font-medium">{data.volume.toLocaleString()}</span>
              </div>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  // Custom candlestick shape
  const CandlestickBar = (props: any) => {
    const { payload, x, y, width, height } = props
    if (!payload) return null

    const candleWidth = Math.max(width * 0.6, 2)
    const wickWidth = 1
    const centerX = x + width / 2

    return (
      <g>
        {/* Wick (high-low line) */}
        <line
          x1={centerX}
          y1={y + height - (payload.high - payload.low) * (height / (payload.high - payload.low))}
          x2={centerX}
          y2={y + height - (payload.low - payload.low) * (height / (payload.high - payload.low))}
          stroke="#666"
          strokeWidth={wickWidth}
        />
        
        {/* Body (open-close rectangle) */}
        <rect
          x={centerX - candleWidth / 2}
          y={y + height - (Math.max(payload.open, payload.close) - payload.low) * (height / (payload.high - payload.low))}
          width={candleWidth}
          height={Math.abs(payload.close - payload.open) * (height / (payload.high - payload.low))}
          fill={payload.isGreen ? '#22c55e' : '#ef4444'}
          stroke={payload.isGreen ? '#16a34a' : '#dc2626'}
          strokeWidth={1}
        />
      </g>
    )
  }

  if (!data || data.length === 0) {
    return (
      <div 
        className="flex items-center justify-center bg-gray-50 rounded-lg"
        style={{ height }}
      >
        <p className="text-gray-500">No chart data available</p>
      </div>
    )
  }

  const chartHeight = showVolume ? height * 0.7 : height
  const volumeHeight = showVolume ? height * 0.3 : 0

  return (
    <div style={{ height }}>
      {/* Price Chart */}
      <div style={{ height: chartHeight }}>
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={transformedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            {showGrid && (
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            )}
            <XAxis 
              dataKey="x"
              tickFormatter={formatXAxisTick}
              stroke="#6b7280"
              fontSize={12}
            />
            <YAxis 
              domain={['dataMin - 1', 'dataMax + 1']}
              stroke="#6b7280"
              fontSize={12}
              tickFormatter={(value) => `$${value.toFixed(2)}`}
            />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            
            <Bar dataKey="wickHeight" shape={<CandlestickBar />} />
          </ComposedChart>
        </ResponsiveContainer>
      </div>

      {/* Volume Chart */}
      {showVolume && (
        <div style={{ height: volumeHeight }}>
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={transformedData} margin={{ top: 0, right: 30, left: 20, bottom: 5 }}>
              <XAxis 
                dataKey="x"
                tickFormatter={formatXAxisTick}
                stroke="#6b7280"
                fontSize={12}
              />
              <YAxis 
                stroke="#6b7280"
                fontSize={12}
                tickFormatter={(value) => {
                  if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
                  if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
                  return value.toString()
                }}
              />
              <Bar dataKey="volume">
                {transformedData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.isGreen ? '#22c55e' : '#ef4444'} />
                ))}
              </Bar>
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  )
}

export default CandlestickChart
