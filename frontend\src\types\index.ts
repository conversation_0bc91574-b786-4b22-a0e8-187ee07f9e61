// User types
export interface User {
  id: number
  email: string
  username: string
  first_name?: string
  last_name?: string
  full_name: string
  is_active: boolean
  is_verified: boolean
  is_premium: boolean
  bio?: string
  avatar_url?: string
  timezone: string
  notification_email: boolean
  notification_push: boolean
  created_at: string
  updated_at?: string
  last_login?: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  email: string
  username: string
  password: string
  first_name?: string
  last_name?: string
}

export interface AuthResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: User
}

// Stock types
export interface Stock {
  id: number
  symbol: string
  name: string
  exchange: string
  sector?: string
  industry?: string
  market_cap?: number
  current_price?: number
  previous_close?: number
  open_price?: number
  day_high?: number
  day_low?: number
  volume?: number
  avg_volume?: number
  price_change?: number
  price_change_percent?: number
  technical_indicators?: Record<string, any>
  pe_ratio?: number
  eps?: number
  dividend_yield?: number
  beta?: number
  is_active: boolean
  is_tradable: boolean
  last_data_update?: string
}

export interface StockPriceHistory {
  id: number
  stock_id: number
  open_price: number
  high_price: number
  low_price: number
  close_price: number
  volume: number
  adjusted_close?: number
  date: string
  timeframe: string
}

// Watchlist types
export interface Watchlist {
  id: number
  user_id: number
  name: string
  description?: string
  color: string
  is_public: boolean
  is_default: boolean
  sort_order: number
  stock_count: number
  created_at: string
  updated_at?: string
}

export interface WatchlistItem {
  id: number
  watchlist_id: number
  stock_id: number
  notes?: string
  target_price?: number
  stop_loss?: number
  position: number
  price_alert_enabled: boolean
  volume_alert_enabled: boolean
  technical_alert_enabled: boolean
  price_alert_above?: number
  price_alert_below?: number
  volume_alert_threshold: number
  created_at: string
  updated_at?: string
  stock?: Stock
}

// Alert types
export type AlertType = 
  | 'price_above'
  | 'price_below'
  | 'volume_surge'
  | 'rsi_oversold'
  | 'rsi_overbought'
  | 'macd_bullish'
  | 'macd_bearish'
  | 'bollinger_squeeze'
  | 'support_break'
  | 'resistance_break'
  | 'momentum_change'
  | 'entry_point'
  | 'custom'

export type AlertStatus = 'active' | 'triggered' | 'dismissed' | 'expired'

export type AlertPriority = 'low' | 'medium' | 'high' | 'critical'

export interface Alert {
  id: number
  user_id: number
  stock_id: number
  alert_type: AlertType
  title: string
  message: string
  conditions: Record<string, any>
  priority: AlertPriority
  status: AlertStatus
  email_notification: boolean
  push_notification: boolean
  trigger_price?: number
  trigger_value?: number
  trigger_data?: Record<string, any>
  expires_at?: string
  created_at: string
  updated_at?: string
  triggered_at?: string
  stock?: Stock
}

// Screening types
export interface ScreeningCriteria {
  min_price?: number
  max_price?: number
  min_volume?: number
  min_market_cap?: number
  max_market_cap?: number
  sectors?: string[]
  technical_filters?: {
    rsi_min?: number
    rsi_max?: number
    volume_surge_threshold?: number
    price_change_threshold?: number
  }
}

export interface ScreeningResult {
  id: number
  stock_id: number
  screening_criteria: ScreeningCriteria
  score: number
  technical_signals?: Record<string, any>
  entry_points?: Record<string, any>
  risk_level: string
  confidence?: number
  created_at: string
  stock?: Stock
}

// WebSocket types
export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
}

export interface StockUpdateMessage extends WebSocketMessage {
  type: 'stock_update'
  data: Stock
}

export interface AlertMessage extends WebSocketMessage {
  type: 'alert'
  data: Alert
}

export interface WatchlistUpdateMessage extends WebSocketMessage {
  type: 'watchlist_update'
  data: Watchlist
}

// API response types
export interface ApiResponse<T> {
  data: T
  message?: string
  status: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// Chart types
export interface ChartDataPoint {
  x: string | number
  y: number
  date?: string
  open?: number
  high?: number
  low?: number
  close?: number
  volume?: number
}

export interface ChartConfig {
  type: 'line' | 'candlestick' | 'bar'
  timeframe: '1m' | '5m' | '15m' | '1h' | '1d' | '1w' | '1M'
  indicators?: string[]
}

// Form types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'checkbox' | 'textarea'
  placeholder?: string
  required?: boolean
  options?: { value: string; label: string }[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
}
