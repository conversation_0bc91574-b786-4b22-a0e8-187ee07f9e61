"""
Tests for technical indicators algorithms.
"""
import pytest
import numpy as np
from app.algorithms.technical_indicators import (
    calculate_sma,
    calculate_ema,
    calculate_rsi,
    calculate_macd,
    calculate_bollinger_bands,
    detect_volume_surge,
    generate_trading_signals
)


def test_calculate_sma():
    """Test Simple Moving Average calculation."""
    prices = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    period = 3
    
    sma = calculate_sma(prices, period)
    
    # First two values should be NaN
    assert np.isnan(sma[0])
    assert np.isnan(sma[1])
    
    # Third value should be average of first 3 prices
    assert sma[2] == 2.0  # (1+2+3)/3
    assert sma[3] == 3.0  # (2+3+4)/3
    assert sma[4] == 4.0  # (3+4+5)/3


def test_calculate_ema():
    """Test Exponential Moving Average calculation."""
    prices = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    period = 3
    
    ema = calculate_ema(prices, period)
    
    # First two values should be NaN
    assert np.isnan(ema[0])
    assert np.isnan(ema[1])
    
    # Third value should be SMA
    assert ema[2] == 2.0  # (1+2+3)/3
    
    # Subsequent values should be EMA
    assert ema[3] > 2.0  # Should be weighted towards recent prices


def test_calculate_rsi():
    """Test RSI calculation."""
    # Create price series with clear trend
    prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89,
              46.03, 46.28, 46.28, 46.00, 46.03, 46.41, 46.22, 45.64]
    
    rsi = calculate_rsi(prices, period=14)
    
    # First 14 values should be NaN
    for i in range(14):
        assert np.isnan(rsi[i])
    
    # RSI should be between 0 and 100
    for i in range(14, len(rsi)):
        if not np.isnan(rsi[i]):
            assert 0 <= rsi[i] <= 100


def test_calculate_macd():
    """Test MACD calculation."""
    prices = list(range(1, 51))  # 50 price points
    
    macd_data = calculate_macd(prices, fast=12, slow=26, signal=9)
    
    assert 'macd' in macd_data
    assert 'signal' in macd_data
    assert 'histogram' in macd_data
    
    # All arrays should have same length as input
    assert len(macd_data['macd']) == len(prices)
    assert len(macd_data['signal']) == len(prices)
    assert len(macd_data['histogram']) == len(prices)


def test_calculate_bollinger_bands():
    """Test Bollinger Bands calculation."""
    prices = [20, 21, 19, 22, 18, 23, 17, 24, 16, 25, 15, 26, 14, 27, 13, 28, 12, 29, 11, 30]
    
    bb = calculate_bollinger_bands(prices, period=10, std_dev=2)
    
    assert 'upper' in bb
    assert 'middle' in bb
    assert 'lower' in bb
    
    # All arrays should have same length as input
    assert len(bb['upper']) == len(prices)
    assert len(bb['middle']) == len(prices)
    assert len(bb['lower']) == len(prices)
    
    # For valid points, upper should be > middle > lower
    for i in range(10, len(prices)):
        if not np.isnan(bb['upper'][i]):
            assert bb['upper'][i] > bb['middle'][i] > bb['lower'][i]


def test_detect_volume_surge():
    """Test volume surge detection."""
    volumes = [1000, 1100, 900, 1200, 800, 1300, 700, 2500, 600, 1400]  # Surge at index 7
    
    surges = detect_volume_surge(volumes, threshold=2.0, period=5)
    
    assert len(surges) == len(volumes)
    assert isinstance(surges[0], bool)


def test_generate_trading_signals():
    """Test trading signal generation."""
    indicators = {
        'rsi': 25,  # Oversold
        'macd': {
            'macd': 0.5,
            'signal': 0.3
        },
        'bollinger_bands': {
            'upper': 105,
            'middle': 100,
            'lower': 95
        }
    }
    
    signals = generate_trading_signals(indicators)
    
    assert 'buy_signals' in signals
    assert 'sell_signals' in signals
    assert 'overall_signal' in signals
    assert 'confidence' in signals
    
    # Should detect RSI oversold as buy signal
    assert 'RSI Oversold' in signals['buy_signals']
    
    # Should detect MACD bullish as buy signal
    assert 'MACD Bullish' in signals['buy_signals']


def test_empty_price_data():
    """Test functions with empty price data."""
    empty_prices = []
    
    assert calculate_sma(empty_prices, 5) == []
    assert calculate_ema(empty_prices, 5) == []
    assert calculate_rsi(empty_prices, 14) == []


def test_insufficient_data():
    """Test functions with insufficient data."""
    short_prices = [1, 2, 3]
    
    sma = calculate_sma(short_prices, 5)
    assert len(sma) == 3
    assert all(np.isnan(x) for x in sma)
    
    rsi = calculate_rsi(short_prices, 14)
    assert len(rsi) == 3
    assert all(np.isnan(x) for x in rsi)
