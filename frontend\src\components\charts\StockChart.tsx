import React from 'react'
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts'
import { format } from 'date-fns'
import { ChartDataPoint } from '@/types'

interface StockChartProps {
  data: ChartDataPoint[]
  height?: number
  showGrid?: boolean
  showTooltip?: boolean
  color?: string
  strokeWidth?: number
  referenceLines?: Array<{
    value: number
    label: string
    color?: string
  }>
}

export const StockChart: React.FC<StockChartProps> = ({
  data,
  height = 300,
  showGrid = true,
  showTooltip = true,
  color = '#3b82f6',
  strokeWidth = 2,
  referenceLines = []
}) => {
  const formatXAxisTick = (tickItem: any) => {
    if (typeof tickItem === 'string') {
      try {
        return format(new Date(tickItem), 'MMM dd')
      } catch {
        return tickItem
      }
    }
    return tickItem
  }

  const formatTooltipLabel = (label: any) => {
    if (typeof label === 'string') {
      try {
        return format(new Date(label), 'M<PERSON> dd, yyyy HH:mm')
      } catch {
        return label
      }
    }
    return label
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">
            {formatTooltipLabel(label)}
          </p>
          <div className="mt-1 space-y-1">
            {data.open && (
              <p className="text-xs text-gray-600">
                Open: <span className="font-medium">${data.open.toFixed(2)}</span>
              </p>
            )}
            {data.high && (
              <p className="text-xs text-gray-600">
                High: <span className="font-medium">${data.high.toFixed(2)}</span>
              </p>
            )}
            {data.low && (
              <p className="text-xs text-gray-600">
                Low: <span className="font-medium">${data.low.toFixed(2)}</span>
              </p>
            )}
            <p className="text-xs text-gray-600">
              Close: <span className="font-medium">${payload[0].value.toFixed(2)}</span>
            </p>
            {data.volume && (
              <p className="text-xs text-gray-600">
                Volume: <span className="font-medium">{data.volume.toLocaleString()}</span>
              </p>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  if (!data || data.length === 0) {
    return (
      <div 
        className="flex items-center justify-center bg-gray-50 rounded-lg"
        style={{ height }}
      >
        <p className="text-gray-500">No chart data available</p>
      </div>
    )
  }

  return (
    <div style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          {showGrid && (
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          )}
          <XAxis 
            dataKey="x"
            tickFormatter={formatXAxisTick}
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis 
            domain={['dataMin - 1', 'dataMax + 1']}
            stroke="#6b7280"
            fontSize={12}
            tickFormatter={(value) => `$${value.toFixed(2)}`}
          />
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          
          {/* Reference lines */}
          {referenceLines.map((line, index) => (
            <ReferenceLine
              key={index}
              y={line.value}
              stroke={line.color || '#ef4444'}
              strokeDasharray="5 5"
              label={{ value: line.label, position: 'topRight' }}
            />
          ))}
          
          <Line
            type="monotone"
            dataKey="y"
            stroke={color}
            strokeWidth={strokeWidth}
            dot={false}
            activeDot={{ r: 4, stroke: color, strokeWidth: 2, fill: '#fff' }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}

export default StockChart
