import { WebSocketMessage, StockUpdateMessage, AlertMessage, WatchlistUpdateMessage } from '@/types'

type MessageHandler = (message: WebSocketMessage) => void
type ConnectionHandler = () => void
type ErrorHandler = (error: Event) => void

class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 1000
  private messageHandlers: Map<string, MessageHandler[]> = new Map()
  private connectionHandlers: ConnectionHandler[] = []
  private disconnectionHandlers: ConnectionHandler[] = []
  private errorHandlers: ErrorHandler[] = []
  private clientId: string | null = null
  private isConnecting = false

  constructor() {
    this.generateClientId()
  }

  private generateClientId(): void {
    const userId = this.getUserId()
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 15)
    this.clientId = `user_${userId}_${timestamp}_${random}`
  }

  private getUserId(): string {
    // Get user ID from auth store or localStorage
    try {
      const authData = localStorage.getItem('auth-storage')
      if (authData) {
        const parsed = JSON.parse(authData)
        return parsed.state?.user?.id?.toString() || 'anonymous'
      }
    } catch (error) {
      console.error('Error getting user ID:', error)
    }
    return 'anonymous'
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      if (this.isConnecting) {
        return
      }

      this.isConnecting = true
      const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/ws/${this.clientId}`

      try {
        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log('WebSocket connected')
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.connectionHandlers.forEach(handler => handler())
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            this.handleMessage(message)
          } catch (error) {
            console.error('Error parsing WebSocket message:', error)
          }
        }

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason)
          this.isConnecting = false
          this.disconnectionHandlers.forEach(handler => handler())
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error)
          this.isConnecting = false
          this.errorHandlers.forEach(handler => handler(error))
          reject(error)
        }
      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    setTimeout(() => {
      this.connect().catch(console.error)
    }, delay)
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
  }

  send(message: any): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  subscribe(messageType: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, [])
    }
    
    this.messageHandlers.get(messageType)!.push(handler)
    
    // Return unsubscribe function
    return () => {
      const handlers = this.messageHandlers.get(messageType)
      if (handlers) {
        const index = handlers.indexOf(handler)
        if (index > -1) {
          handlers.splice(index, 1)
        }
      }
    }
  }

  subscribeToStock(symbol: string): void {
    this.send({
      type: 'subscribe',
      topic: `stock:${symbol}`
    })
  }

  unsubscribeFromStock(symbol: string): void {
    this.send({
      type: 'unsubscribe',
      topic: `stock:${symbol}`
    })
  }

  onConnect(handler: ConnectionHandler): () => void {
    this.connectionHandlers.push(handler)
    return () => {
      const index = this.connectionHandlers.indexOf(handler)
      if (index > -1) {
        this.connectionHandlers.splice(index, 1)
      }
    }
  }

  onDisconnect(handler: ConnectionHandler): () => void {
    this.disconnectionHandlers.push(handler)
    return () => {
      const index = this.disconnectionHandlers.indexOf(handler)
      if (index > -1) {
        this.disconnectionHandlers.splice(index, 1)
      }
    }
  }

  onError(handler: ErrorHandler): () => void {
    this.errorHandlers.push(handler)
    return () => {
      const index = this.errorHandlers.indexOf(handler)
      if (index > -1) {
        this.errorHandlers.splice(index, 1)
      }
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    const handlers = this.messageHandlers.get(message.type)
    if (handlers) {
      handlers.forEach(handler => handler(message))
    }

    // Handle specific message types
    switch (message.type) {
      case 'pong':
        // Handle pong response
        break
      case 'stock_update':
        this.handleStockUpdate(message as StockUpdateMessage)
        break
      case 'alert':
        this.handleAlert(message as AlertMessage)
        break
      case 'watchlist_update':
        this.handleWatchlistUpdate(message as WatchlistUpdateMessage)
        break
    }
  }

  private handleStockUpdate(message: StockUpdateMessage): void {
    // Emit custom event for stock updates
    window.dispatchEvent(new CustomEvent('stockUpdate', { detail: message.data }))
  }

  private handleAlert(message: AlertMessage): void {
    // Emit custom event for alerts
    window.dispatchEvent(new CustomEvent('alertReceived', { detail: message.data }))
  }

  private handleWatchlistUpdate(message: WatchlistUpdateMessage): void {
    // Emit custom event for watchlist updates
    window.dispatchEvent(new CustomEvent('watchlistUpdate', { detail: message.data }))
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  getConnectionState(): string {
    if (!this.ws) return 'CLOSED'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING'
      case WebSocket.OPEN:
        return 'OPEN'
      case WebSocket.CLOSING:
        return 'CLOSING'
      case WebSocket.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  }

  // Heartbeat to keep connection alive
  startHeartbeat(): void {
    setInterval(() => {
      if (this.isConnected()) {
        this.send({ type: 'ping' })
      }
    }, 30000) // Send ping every 30 seconds
  }
}

// Export singleton instance
export const websocketService = new WebSocketService()
