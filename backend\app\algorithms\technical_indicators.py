"""
Custom implementation of technical indicators.
"""
from typing import List, Dict, Any, Optional, Tuple
import math
from ..config import TECHNICAL_INDICATORS

# Simple replacement for numpy functions
def mean(values: List[float]) -> float:
    """Calculate mean of a list."""
    return sum(values) / len(values) if values else 0.0

def std(values: List[float]) -> float:
    """Calculate standard deviation."""
    if len(values) < 2:
        return 0.0
    avg = mean(values)
    variance = sum((x - avg) ** 2 for x in values) / len(values)
    return math.sqrt(variance)

def isnan(value) -> bool:
    """Check if value is NaN."""
    return value != value or value is None

def linear_regression(x_values: List[float], y_values: List[float]) -> Tuple[float, float]:
    """
    Calculate linear regression slope and intercept.
    Returns (slope, intercept)
    """
    if len(x_values) != len(y_values) or len(x_values) < 2:
        return 0.0, 0.0

    n = len(x_values)
    sum_x = sum(x_values)
    sum_y = sum(y_values)
    sum_xy = sum(x * y for x, y in zip(x_values, y_values))
    sum_x2 = sum(x * x for x in x_values)

    # Calculate slope
    denominator = n * sum_x2 - sum_x * sum_x
    if denominator == 0:
        return 0.0, mean(y_values)

    slope = (n * sum_xy - sum_x * sum_y) / denominator
    intercept = (sum_y - slope * sum_x) / n

    return slope, intercept

# NaN replacement
NaN = float('nan')


def calculate_sma(prices: List[float], period: int) -> List[float]:
    """Calculate Simple Moving Average."""
    if len(prices) < period:
        return [NaN] * len(prices)

    sma = []
    for i in range(len(prices)):
        if i < period - 1:
            sma.append(NaN)
        else:
            sma.append(mean(prices[i - period + 1:i + 1]))

    return sma


def calculate_ema(prices: List[float], period: int) -> List[float]:
    """Calculate Exponential Moving Average."""
    if len(prices) < period:
        return [NaN] * len(prices)

    multiplier = 2 / (period + 1)
    ema = [NaN] * (period - 1)
    ema.append(mean(prices[:period]))  # First EMA is SMA

    for i in range(period, len(prices)):
        ema.append((prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier)))

    return ema


def calculate_rsi(prices: List[float], period: int = 14) -> List[float]:
    """Calculate Relative Strength Index."""
    if len(prices) < period + 1:
        return [NaN] * len(prices)

    # Calculate price changes
    deltas = [prices[i] - prices[i - 1] for i in range(1, len(prices))]

    # Separate gains and losses
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]

    # Calculate initial average gain and loss
    avg_gain = mean(gains[:period])
    avg_loss = mean(losses[:period])

    rsi = [NaN] * (period + 1)
    
    # Calculate RSI for each point
    for i in range(period, len(deltas)):
        if i == period:
            # First RSI calculation
            if avg_loss == 0:
                rsi.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi.append(100 - (100 / (1 + rs)))
        else:
            # Subsequent RSI calculations using smoothed averages
            avg_gain = ((avg_gain * (period - 1)) + gains[i]) / period
            avg_loss = ((avg_loss * (period - 1)) + losses[i]) / period
            
            if avg_loss == 0:
                rsi.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi.append(100 - (100 / (1 + rs)))
    
    return rsi


def calculate_macd(prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, List[float]]:
    """Calculate MACD (Moving Average Convergence Divergence)."""
    if len(prices) < slow:
        return {
            'macd': [NaN] * len(prices),
            'signal': [NaN] * len(prices),
            'histogram': [NaN] * len(prices)
        }
    
    # Calculate EMAs
    ema_fast = calculate_ema(prices, fast)
    ema_slow = calculate_ema(prices, slow)
    
    # Calculate MACD line
    macd = []
    for i in range(len(prices)):
        if isnan(ema_fast[i]) or isnan(ema_slow[i]):
            macd.append(NaN)
        else:
            macd.append(ema_fast[i] - ema_slow[i])
    
    # Calculate signal line (EMA of MACD)
    signal_line = calculate_ema([x for x in macd if not isnan(x)], signal)
    
    # Pad signal line to match length
    signal_padded = [NaN] * (len(macd) - len(signal_line)) + signal_line
    
    # Calculate histogram
    histogram = []
    for i in range(len(macd)):
        if isnan(macd[i]) or isnan(signal_padded[i]):
            histogram.append(NaN)
        else:
            histogram.append(macd[i] - signal_padded[i])
    
    return {
        'macd': macd,
        'signal': signal_padded,
        'histogram': histogram
    }


def calculate_bollinger_bands(prices: List[float], period: int = 20, std_dev: float = 2) -> Dict[str, List[float]]:
    """Calculate Bollinger Bands."""
    if len(prices) < period:
        return {
            'upper': [NaN] * len(prices),
            'middle': [NaN] * len(prices),
            'lower': [NaN] * len(prices)
        }
    
    sma = calculate_sma(prices, period)
    
    upper = []
    lower = []
    
    for i in range(len(prices)):
        if i < period - 1:
            upper.append(NaN)
            lower.append(NaN)
        else:
            # Calculate standard deviation for the period
            period_prices = prices[i - period + 1:i + 1]
            std_value = std(period_prices)

            upper.append(sma[i] + (std_dev * std_value))
            lower.append(sma[i] - (std_dev * std_value))
    
    return {
        'upper': upper,
        'middle': sma,
        'lower': lower
    }


def calculate_volume_sma(volumes: List[int], period: int = 20) -> List[float]:
    """Calculate Simple Moving Average of volume."""
    return calculate_sma([float(v) for v in volumes], period)


def detect_volume_surge(volumes: List[int], threshold: float = 2.0, period: int = 20) -> List[bool]:
    """Detect volume surges above average."""
    if len(volumes) < period:
        return [False] * len(volumes)
    
    avg_volume = calculate_volume_sma(volumes, period)
    surges = []
    
    for i, volume in enumerate(volumes):
        if i < period - 1 or isnan(avg_volume[i]):
            surges.append(False)
        else:
            surges.append(volume > (avg_volume[i] * threshold))
    
    return surges


def calculate_support_resistance(highs: List[float], lows: List[float], period: int = 20) -> Dict[str, List[float]]:
    """Calculate support and resistance levels."""
    if len(highs) < period or len(lows) < period:
        return {
            'support': [NaN] * len(lows),
            'resistance': [NaN] * len(highs)
        }
    
    support = []
    resistance = []
    
    for i in range(len(highs)):
        if i < period - 1:
            support.append(NaN)
            resistance.append(NaN)
        else:
            # Calculate support as minimum of recent lows
            period_lows = lows[i - period + 1:i + 1]
            support.append(min(period_lows))
            
            # Calculate resistance as maximum of recent highs
            period_highs = highs[i - period + 1:i + 1]
            resistance.append(max(period_highs))
    
    return {
        'support': support,
        'resistance': resistance
    }


def calculate_all_indicators(price_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate all technical indicators for given price data."""
    if not price_data:
        return {}
    
    # Extract price arrays
    closes = [float(d['close']) for d in price_data]
    highs = [float(d['high']) for d in price_data]
    lows = [float(d['low']) for d in price_data]
    volumes = [int(d['volume']) for d in price_data]
    
    # Get indicator parameters from config
    rsi_config = TECHNICAL_INDICATORS['rsi']
    macd_config = TECHNICAL_INDICATORS['macd']
    bb_config = TECHNICAL_INDICATORS['bollinger_bands']
    ma_config = TECHNICAL_INDICATORS['moving_averages']
    vol_config = TECHNICAL_INDICATORS['volume_sma']
    
    # Calculate all indicators
    indicators = {
        'rsi': calculate_rsi(closes, rsi_config['period']),
        'macd': calculate_macd(closes, macd_config['fast'], macd_config['slow'], macd_config['signal']),
        'bollinger_bands': calculate_bollinger_bands(closes, bb_config['period'], bb_config['std_dev']),
        'sma_20': calculate_sma(closes, ma_config['short']),
        'sma_50': calculate_sma(closes, ma_config['medium']),
        'sma_200': calculate_sma(closes, ma_config['long']),
        'ema_12': calculate_ema(closes, 12),
        'ema_26': calculate_ema(closes, 26),
        'volume_sma': calculate_volume_sma(volumes, vol_config['period']),
        'volume_surge': detect_volume_surge(volumes),
        'support_resistance': calculate_support_resistance(highs, lows)
    }
    
    # Get latest values (most recent)
    latest_indicators = {}
    for key, value in indicators.items():
        if isinstance(value, dict):
            latest_indicators[key] = {k: v[-1] if v and not isnan(v[-1]) else None for k, v in value.items()}
        elif isinstance(value, list):
            latest_indicators[key] = value[-1] if value and not isnan(value[-1]) else None
        else:
            latest_indicators[key] = value
    
    return latest_indicators


def generate_trading_signals(indicators: Dict[str, Any]) -> Dict[str, Any]:
    """Generate trading signals based on technical indicators."""
    signals = {
        'buy_signals': [],
        'sell_signals': [],
        'neutral_signals': [],
        'overall_signal': 'NEUTRAL',
        'confidence': 0.0
    }
    
    rsi = indicators.get('rsi')
    macd = indicators.get('macd', {})
    bb = indicators.get('bollinger_bands', {})
    
    # RSI signals
    if rsi is not None:
        if rsi < 30:
            signals['buy_signals'].append('RSI Oversold')
        elif rsi > 70:
            signals['sell_signals'].append('RSI Overbought')
    
    # MACD signals
    macd_line = macd.get('macd')
    signal_line = macd.get('signal')
    if macd_line is not None and signal_line is not None:
        if macd_line > signal_line:
            signals['buy_signals'].append('MACD Bullish')
        else:
            signals['sell_signals'].append('MACD Bearish')
    
    # Bollinger Bands signals
    bb_upper = bb.get('upper')
    bb_lower = bb.get('lower')
    bb_middle = bb.get('middle')
    
    # Calculate overall signal
    buy_count = len(signals['buy_signals'])
    sell_count = len(signals['sell_signals'])
    
    if buy_count > sell_count:
        signals['overall_signal'] = 'BUY'
        signals['confidence'] = min(0.9, (buy_count - sell_count) * 0.3)
    elif sell_count > buy_count:
        signals['overall_signal'] = 'SELL'
        signals['confidence'] = min(0.9, (sell_count - buy_count) * 0.3)
    else:
        signals['overall_signal'] = 'NEUTRAL'
        signals['confidence'] = 0.1
    
    return signals
