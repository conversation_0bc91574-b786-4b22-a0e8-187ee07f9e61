"""
Entry point detection algorithms for identifying optimal stock entry points.
"""
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from .technical_indicators import (
    calculate_rsi, calculate_macd, calculate_bollinger_bands,
    calculate_sma, calculate_ema, calculate_support_resistance,
    mean, isnan, NaN, linear_regression
)
from ..utils.logger import get_logger

logger = get_logger(__name__)


class EntryPointDetector:
    """Detects optimal entry points for stock trading."""
    
    def __init__(self):
        self.confidence_threshold = 0.7
        self.min_data_points = 50
    
    def detect_entry_points(self, historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Detect entry points based on multiple technical indicators.

        Args:
            historical_data: List of historical price data

        Returns:
            Dictionary containing entry point analysis
        """
        if len(historical_data) < self.min_data_points:
            return {"error": "Insufficient data for analysis"}

        # Sort data by date
        sorted_data = sorted(historical_data, key=lambda x: x['date'])

        # Calculate technical indicators
        indicators = self._calculate_indicators(sorted_data)
        
        # Detect various entry patterns
        entry_signals = {
            'bullish_divergence': self._detect_bullish_divergence(sorted_data, indicators),
            'oversold_bounce': self._detect_oversold_bounce(sorted_data, indicators),
            'breakout_entry': self._detect_breakout_entry(sorted_data, indicators),
            'golden_cross': self._detect_golden_cross(sorted_data, indicators),
            'support_bounce': self._detect_support_bounce(sorted_data, indicators),
            'bollinger_squeeze': self._detect_bollinger_squeeze(sorted_data, indicators)
        }

        # Calculate overall entry score
        entry_score = self._calculate_entry_score(entry_signals)

        # Generate entry recommendation
        recommendation = self._generate_recommendation(entry_signals, entry_score)

        return {
            'entry_signals': entry_signals,
            'entry_score': entry_score,
            'recommendation': recommendation,
            'current_price': float(sorted_data[-1]['close']),
            'analysis_date': datetime.utcnow().isoformat(),
            'indicators': indicators
        }
    
    def _calculate_indicators(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate all required technical indicators."""
        closes = [float(d['close']) for d in data]
        highs = [float(d['high']) for d in data]
        lows = [float(d['low']) for d in data]
        volumes = [int(d['volume']) for d in data]

        return {
            'rsi': calculate_rsi(closes, 14),
            'macd': calculate_macd(closes, 12, 26, 9),
            'bollinger_bands': calculate_bollinger_bands(closes, 20, 2),
            'sma_20': calculate_sma(closes, 20),
            'sma_50': calculate_sma(closes, 50),
            'ema_12': calculate_ema(closes, 12),
            'ema_26': calculate_ema(closes, 26),
            'support_resistance': calculate_support_resistance(highs, lows)
        }
    
    def _detect_bullish_divergence(self, data: List[Dict[str, Any]], indicators: Dict) -> Dict[str, Any]:
        """Detect bullish divergence between price and RSI."""
        try:
            rsi = indicators['rsi']
            closes = [float(d['close']) for d in data]

            if len(rsi) < 20:
                return {'detected': False, 'confidence': 0.0}

            # Look for price making lower lows while RSI makes higher lows
            recent_period = 10
            price_recent = closes[-recent_period:]
            rsi_recent = [r for r in rsi[-recent_period:] if not isnan(r)]

            if len(rsi_recent) < recent_period:
                return {'detected': False, 'confidence': 0.0}

            # Calculate trends using linear regression
            x_values = list(range(len(price_recent)))
            price_slope, _ = linear_regression([float(x) for x in x_values], price_recent)
            rsi_slope, _ = linear_regression([float(x) for x in x_values], rsi_recent)

            # Bullish divergence: price trending down, RSI trending up
            current_rsi = rsi[-1] if not isnan(rsi[-1]) else 50
            divergence_detected = price_slope < 0 and rsi_slope > 0 and current_rsi < 40

            confidence = 0.8 if divergence_detected else 0.0

            return {
                'detected': divergence_detected,
                'confidence': confidence,
                'description': 'Price making lower lows while RSI makes higher lows'
            }

        except Exception as e:
            logger.error(f"Error detecting bullish divergence: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_oversold_bounce(self, data: List[Dict[str, Any]], indicators: Dict) -> Dict[str, Any]:
        """Detect oversold bounce opportunity."""
        try:
            rsi = indicators['rsi']

            if len(rsi) < 5:
                return {'detected': False, 'confidence': 0.0}

            current_rsi = rsi[-1] if not isnan(rsi[-1]) else 50
            prev_rsi = rsi[-2] if len(rsi) > 1 and not isnan(rsi[-2]) else current_rsi

            # RSI below 30 and starting to turn up
            oversold_bounce = current_rsi < 35 and current_rsi > prev_rsi and current_rsi > 25

            confidence = 0.7 if oversold_bounce else 0.0

            return {
                'detected': oversold_bounce,
                'confidence': confidence,
                'current_rsi': current_rsi,
                'description': 'RSI oversold and starting to recover'
            }

        except Exception as e:
            logger.error(f"Error detecting oversold bounce: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_breakout_entry(self, data: List[Dict[str, Any]], indicators: Dict) -> Dict[str, Any]:
        """Detect breakout above resistance with volume confirmation."""
        try:
            closes = [float(d['close']) for d in data]
            volumes = [int(d['volume']) for d in data]
            support_resistance = indicators['support_resistance']

            if len(closes) < 20:
                return {'detected': False, 'confidence': 0.0}

            current_price = closes[-1]
            avg_volume = mean(volumes[-20:])
            current_volume = volumes[-1]

            # Check if price broke above recent resistance
            resistance_levels = support_resistance.get('resistance', [])
            if not resistance_levels:
                return {'detected': False, 'confidence': 0.0}

            # Filter out NaN values and find nearest resistance
            valid_resistance = [r for r in resistance_levels if not isnan(r)]
            if not valid_resistance:
                return {'detected': False, 'confidence': 0.0}

            nearest_resistance = min(valid_resistance, key=lambda x: abs(x - current_price))

            # Breakout conditions
            price_breakout = current_price > nearest_resistance * 1.01  # 1% above resistance
            volume_confirmation = current_volume > avg_volume * 1.5

            breakout_detected = price_breakout and volume_confirmation
            confidence = 0.8 if breakout_detected else 0.0

            return {
                'detected': breakout_detected,
                'confidence': confidence,
                'resistance_level': nearest_resistance,
                'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 0,
                'description': 'Price breakout above resistance with volume confirmation'
            }

        except Exception as e:
            logger.error(f"Error detecting breakout entry: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_golden_cross(self, data: List[Dict[str, Any]], indicators: Dict) -> Dict[str, Any]:
        """Detect golden cross (20 SMA crossing above 50 SMA)."""
        try:
            sma_20 = indicators['sma_20']
            sma_50 = indicators['sma_50']

            if len(sma_20) < 2 or len(sma_50) < 2:
                return {'detected': False, 'confidence': 0.0}

            # Check if 20 SMA crossed above 50 SMA recently
            current_20 = sma_20[-1] if not isnan(sma_20[-1]) else 0
            current_50 = sma_50[-1] if not isnan(sma_50[-1]) else 0
            prev_20 = sma_20[-2] if not isnan(sma_20[-2]) else 0
            prev_50 = sma_50[-2] if not isnan(sma_50[-2]) else 0

            golden_cross = (current_20 > current_50 and prev_20 <= prev_50 and
                          current_20 > 0 and current_50 > 0)

            confidence = 0.6 if golden_cross else 0.0

            return {
                'detected': golden_cross,
                'confidence': confidence,
                'sma_20': current_20,
                'sma_50': current_50,
                'description': '20 SMA crossed above 50 SMA'
            }

        except Exception as e:
            logger.error(f"Error detecting golden cross: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_support_bounce(self, data: List[Dict[str, Any]], indicators: Dict) -> Dict[str, Any]:
        """Detect bounce from support level."""
        try:
            closes = [float(d['close']) for d in data]
            lows = [float(d['low']) for d in data]
            support_resistance = indicators['support_resistance']

            if len(closes) < 10:
                return {'detected': False, 'confidence': 0.0}

            current_price = closes[-1]
            recent_low = min(lows[-5:])

            support_levels = support_resistance.get('support', [])
            if not support_levels:
                return {'detected': False, 'confidence': 0.0}

            # Filter out NaN values and find nearest support
            valid_support = [s for s in support_levels if not isnan(s) and s > 0]
            if not valid_support:
                return {'detected': False, 'confidence': 0.0}

            nearest_support = min(valid_support, key=lambda x: abs(x - recent_low))

            # Check if price bounced from support
            touched_support = abs(recent_low - nearest_support) / nearest_support < 0.02  # Within 2%
            bouncing_up = current_price > recent_low * 1.01  # 1% above recent low

            support_bounce = touched_support and bouncing_up
            confidence = 0.7 if support_bounce else 0.0

            return {
                'detected': support_bounce,
                'confidence': confidence,
                'support_level': nearest_support,
                'recent_low': recent_low,
                'description': 'Price bouncing from support level'
            }

        except Exception as e:
            logger.error(f"Error detecting support bounce: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _detect_bollinger_squeeze(self, data: List[Dict[str, Any]], indicators: Dict) -> Dict[str, Any]:
        """Detect Bollinger Bands squeeze (low volatility before breakout)."""
        try:
            bollinger = indicators['bollinger_bands']

            if not bollinger or len(bollinger['upper']) < 20:
                return {'detected': False, 'confidence': 0.0}

            upper = bollinger['upper']
            lower = bollinger['lower']

            # Calculate band width, filtering out NaN values
            band_widths = []
            for u, l in zip(upper, lower):
                if not isnan(u) and not isnan(l) and (u + l) > 0:
                    band_widths.append((u - l) / ((u + l) / 2))
                else:
                    band_widths.append(NaN)

            if len(band_widths) < 20:
                return {'detected': False, 'confidence': 0.0}

            # Get valid band widths for calculation
            valid_widths = [w for w in band_widths[-20:] if not isnan(w)]
            if len(valid_widths) < 10:
                return {'detected': False, 'confidence': 0.0}

            current_width = band_widths[-1] if not isnan(band_widths[-1]) else 0
            avg_width = mean(valid_widths)

            # Squeeze detected if current width is significantly below average
            squeeze_detected = current_width > 0 and avg_width > 0 and current_width < avg_width * 0.7

            confidence = 0.5 if squeeze_detected else 0.0

            return {
                'detected': squeeze_detected,
                'confidence': confidence,
                'current_width': current_width,
                'avg_width': avg_width,
                'description': 'Bollinger Bands squeeze indicating potential breakout'
            }

        except Exception as e:
            logger.error(f"Error detecting Bollinger squeeze: {e}")
            return {'detected': False, 'confidence': 0.0}
    
    def _calculate_entry_score(self, entry_signals: Dict[str, Dict]) -> float:
        """Calculate overall entry score based on all signals."""
        total_score = 0.0
        signal_count = 0
        
        for signal_name, signal_data in entry_signals.items():
            if signal_data.get('detected', False):
                confidence = signal_data.get('confidence', 0.0)
                total_score += confidence
                signal_count += 1
        
        # Normalize score
        if signal_count > 0:
            return min(total_score / len(entry_signals), 1.0)
        
        return 0.0
    
    def _generate_recommendation(self, entry_signals: Dict, entry_score: float) -> Dict[str, Any]:
        """Generate entry recommendation based on analysis."""
        if entry_score >= 0.7:
            action = "STRONG_BUY"
            reason = "Multiple strong entry signals detected"
        elif entry_score >= 0.5:
            action = "BUY"
            reason = "Good entry signals present"
        elif entry_score >= 0.3:
            action = "WATCH"
            reason = "Some positive signals, monitor closely"
        else:
            action = "HOLD"
            reason = "No strong entry signals detected"
        
        # Count active signals
        active_signals = [name for name, data in entry_signals.items() if data.get('detected', False)]
        
        return {
            'action': action,
            'reason': reason,
            'entry_score': entry_score,
            'active_signals': active_signals,
            'signal_count': len(active_signals)
        }


def detect_entry_point(historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Convenience function to detect entry points.
    
    Args:
        historical_data: List of historical price data
        
    Returns:
        Entry point analysis
    """
    detector = EntryPointDetector()
    return detector.detect_entry_points(historical_data)
