"""
Tests for entry point detection algorithms.
"""
import pytest
from app.algorithms.entry_point_detector import EntryPointDetector, detect_entry_point


def create_test_data(num_points=60):
    """Create test historical data."""
    test_data = []
    base_price = 100
    
    for i in range(num_points):
        # Create some price movement
        price_change = (i % 10 - 5) * 0.5  # Oscillating pattern
        current_price = base_price + price_change + (i * 0.1)  # Slight upward trend
        
        test_data.append({
            'date': f'2024-01-{i+1:02d}',
            'open': current_price - 0.5,
            'high': current_price + 1.0,
            'low': current_price - 1.0,
            'close': current_price,
            'volume': 1000000 + (i * 10000)
        })
    
    return test_data


def test_entry_point_detector_initialization():
    """Test EntryPointDetector initialization."""
    detector = EntryPointDetector()
    assert detector.confidence_threshold == 0.7
    assert detector.min_data_points == 50


def test_detect_entry_points_insufficient_data():
    """Test entry point detection with insufficient data."""
    detector = EntryPointDetector()
    test_data = create_test_data(10)  # Less than min_data_points
    
    result = detector.detect_entry_points(test_data)
    assert "error" in result
    assert "Insufficient data" in result["error"]


def test_detect_entry_points_success():
    """Test successful entry point detection."""
    detector = EntryPointDetector()
    test_data = create_test_data(60)  # Sufficient data
    
    result = detector.detect_entry_points(test_data)
    
    # Check required fields
    assert "entry_signals" in result
    assert "entry_score" in result
    assert "recommendation" in result
    assert "current_price" in result
    assert "analysis_date" in result
    assert "indicators" in result
    
    # Check entry signals structure
    entry_signals = result["entry_signals"]
    expected_signals = [
        'bullish_divergence', 'oversold_bounce', 'breakout_entry',
        'golden_cross', 'support_bounce', 'bollinger_squeeze'
    ]
    
    for signal in expected_signals:
        assert signal in entry_signals
        assert "detected" in entry_signals[signal]
        assert "confidence" in entry_signals[signal]
    
    # Check recommendation structure
    recommendation = result["recommendation"]
    assert "action" in recommendation
    assert "reason" in recommendation
    assert "entry_score" in recommendation
    assert "active_signals" in recommendation
    assert "signal_count" in recommendation
    
    # Check entry score is valid
    assert 0.0 <= result["entry_score"] <= 1.0
    
    # Check current price is valid
    assert result["current_price"] > 0


def test_detect_entry_points_oversold_scenario():
    """Test entry point detection with oversold scenario."""
    detector = EntryPointDetector()
    
    # Create data with declining prices (potential oversold)
    test_data = []
    base_price = 100
    
    for i in range(60):
        # Declining price trend
        current_price = base_price - (i * 0.5)
        
        test_data.append({
            'date': f'2024-01-{i+1:02d}',
            'open': current_price + 0.5,
            'high': current_price + 1.0,
            'low': current_price - 1.0,
            'close': current_price,
            'volume': 1000000
        })
    
    result = detector.detect_entry_points(test_data)
    
    # Should detect some signals due to declining prices
    assert result["entry_score"] >= 0.0
    assert "entry_signals" in result


def test_convenience_function():
    """Test the convenience function detect_entry_point."""
    test_data = create_test_data(60)
    
    result = detect_entry_point(test_data)
    
    # Should have same structure as class method
    assert "entry_signals" in result
    assert "entry_score" in result
    assert "recommendation" in result


def test_entry_signals_confidence_values():
    """Test that confidence values are within valid range."""
    detector = EntryPointDetector()
    test_data = create_test_data(60)
    
    result = detector.detect_entry_points(test_data)
    entry_signals = result["entry_signals"]
    
    for signal_name, signal_data in entry_signals.items():
        confidence = signal_data.get("confidence", 0.0)
        assert 0.0 <= confidence <= 1.0, f"Invalid confidence for {signal_name}: {confidence}"


def test_recommendation_actions():
    """Test that recommendation actions are valid."""
    detector = EntryPointDetector()
    test_data = create_test_data(60)
    
    result = detector.detect_entry_points(test_data)
    recommendation = result["recommendation"]
    
    valid_actions = ["STRONG_BUY", "BUY", "WATCH", "HOLD"]
    assert recommendation["action"] in valid_actions


def test_indicators_calculation():
    """Test that technical indicators are calculated."""
    detector = EntryPointDetector()
    test_data = create_test_data(60)
    
    result = detector.detect_entry_points(test_data)
    indicators = result["indicators"]
    
    # Check that key indicators are present
    expected_indicators = [
        'rsi', 'macd', 'bollinger_bands', 'sma_20', 'sma_50',
        'ema_12', 'ema_26', 'support_resistance'
    ]
    
    for indicator in expected_indicators:
        assert indicator in indicators, f"Missing indicator: {indicator}"


def test_entry_point_analysis_integration():
    """Test integration with the service layer."""
    from app.services.stock_service import stock_service
    from app.schemas.stock import EntryPointAnalysis

    # Test that the service method exists and can be called
    assert hasattr(stock_service, 'analyze_entry_point')

    # Test schema validation
    test_data = {
        "symbol": "AAPL",
        "current_price": 175.43,
        "analysis_date": "2024-01-15T10:30:00+00:00",
        "entry_signals": {
            "oversold_bounce": {
                "detected": True,
                "confidence": 0.7,
                "description": "RSI oversold and starting to recover",
                "current_rsi": 28.5
            }
        },
        "entry_score": 0.65,
        "recommendation": {
            "action": "BUY",
            "reason": "Good entry signals present",
            "entry_score": 0.65,
            "active_signals": ["oversold_bounce"],
            "signal_count": 1
        },
        "indicators": {
            "rsi": 28.5,
            "macd": {"macd": -0.5, "signal": -0.3, "histogram": -0.2}
        }
    }

    # Should not raise validation errors
    analysis = EntryPointAnalysis(**test_data)
    assert analysis.symbol == "AAPL"
    assert analysis.entry_score == 0.65
    assert analysis.recommendation.action == "BUY"
