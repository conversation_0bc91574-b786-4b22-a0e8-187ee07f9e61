import React, { useState } from 'react'
import { Search, Filter, Download, Play } from 'lucide-react'

const sectors = [
  'Technology',
  'Healthcare',
  'Financial Services',
  'Consumer Cyclical',
  'Communication Services',
  'Industrials',
  'Consumer Defensive',
  'Energy',
  'Utilities',
  'Real Estate',
  'Materials',
]

export const Screener: React.FC = () => {
  const [criteria, setCriteria] = useState({
    minPrice: '',
    maxPrice: '',
    minVolume: '',
    minMarketCap: '',
    maxMarketCap: '',
    sectors: [] as string[],
    rsiMin: '',
    rsiMax: '',
    volumeSurgeThreshold: '',
    priceChangeThreshold: '',
  })

  const [results, setResults] = useState([])
  const [isScreening, setIsScreening] = useState(false)

  const handleSectorChange = (sector: string) => {
    setCriteria(prev => ({
      ...prev,
      sectors: prev.sectors.includes(sector)
        ? prev.sectors.filter(s => s !== sector)
        : [...prev.sectors, sector]
    }))
  }

  const handleScreen = async () => {
    setIsScreening(true)
    // Simulate API call
    setTimeout(() => {
      setIsScreening(false)
      // Mock results would be set here
    }, 2000)
  }

  const handleReset = () => {
    setCriteria({
      minPrice: '',
      maxPrice: '',
      minVolume: '',
      minMarketCap: '',
      maxMarketCap: '',
      sectors: [],
      rsiMin: '',
      rsiMax: '',
      volumeSurgeThreshold: '',
      priceChangeThreshold: '',
    })
    setResults([])
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Stock Screener</h1>
          <p className="text-gray-600 mt-1">
            Filter and discover stocks based on your criteria
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="btn-outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button className="btn-outline" onClick={handleReset}>
            Reset
          </button>
          <button 
            className="btn-primary"
            onClick={handleScreen}
            disabled={isScreening}
          >
            {isScreening ? (
              <div className="spinner mr-2" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            {isScreening ? 'Screening...' : 'Run Screen'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters Panel */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Screening Criteria
              </h3>
            </div>
            <div className="card-body space-y-6">
              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    className="input"
                    value={criteria.minPrice}
                    onChange={(e) => setCriteria(prev => ({ ...prev, minPrice: e.target.value }))}
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    className="input"
                    value={criteria.maxPrice}
                    onChange={(e) => setCriteria(prev => ({ ...prev, maxPrice: e.target.value }))}
                  />
                </div>
              </div>

              {/* Volume */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Volume
                </label>
                <input
                  type="number"
                  placeholder="e.g., 1000000"
                  className="input"
                  value={criteria.minVolume}
                  onChange={(e) => setCriteria(prev => ({ ...prev, minVolume: e.target.value }))}
                />
              </div>

              {/* Market Cap */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Market Cap Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="Min (M)"
                    className="input"
                    value={criteria.minMarketCap}
                    onChange={(e) => setCriteria(prev => ({ ...prev, minMarketCap: e.target.value }))}
                  />
                  <input
                    type="number"
                    placeholder="Max (M)"
                    className="input"
                    value={criteria.maxMarketCap}
                    onChange={(e) => setCriteria(prev => ({ ...prev, maxMarketCap: e.target.value }))}
                  />
                </div>
              </div>

              {/* Sectors */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sectors
                </label>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {sectors.map((sector) => (
                    <label key={sector} className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        checked={criteria.sectors.includes(sector)}
                        onChange={() => handleSectorChange(sector)}
                      />
                      <span className="ml-2 text-sm text-gray-700">{sector}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Technical Indicators */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  RSI Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    min="0"
                    max="100"
                    className="input"
                    value={criteria.rsiMin}
                    onChange={(e) => setCriteria(prev => ({ ...prev, rsiMin: e.target.value }))}
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    min="0"
                    max="100"
                    className="input"
                    value={criteria.rsiMax}
                    onChange={(e) => setCriteria(prev => ({ ...prev, rsiMax: e.target.value }))}
                  />
                </div>
              </div>

              {/* Volume Surge */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Volume Surge Threshold
                </label>
                <input
                  type="number"
                  placeholder="e.g., 2.0 (2x average)"
                  step="0.1"
                  className="input"
                  value={criteria.volumeSurgeThreshold}
                  onChange={(e) => setCriteria(prev => ({ ...prev, volumeSurgeThreshold: e.target.value }))}
                />
              </div>

              {/* Price Change */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Min Price Change (%)
                </label>
                <input
                  type="number"
                  placeholder="e.g., 5.0"
                  step="0.1"
                  className="input"
                  value={criteria.priceChangeThreshold}
                  onChange={(e) => setCriteria(prev => ({ ...prev, priceChangeThreshold: e.target.value }))}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Results Panel */}
        <div className="lg:col-span-3">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  Screening Results
                </h3>
                <div className="text-sm text-gray-500">
                  {results.length} stocks found
                </div>
              </div>
            </div>
            <div className="card-body">
              {isScreening ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="spinner mx-auto mb-4" />
                    <p className="text-gray-600">Screening stocks...</p>
                    <p className="text-sm text-gray-500 mt-1">
                      This may take up to 30 seconds
                    </p>
                  </div>
                </div>
              ) : results.length === 0 ? (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No results yet</p>
                  <p className="text-sm text-gray-500 mt-1">
                    Set your criteria and run a screen to find stocks
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="table">
                    <thead className="table-header">
                      <tr>
                        <th className="table-header-cell">Symbol</th>
                        <th className="table-header-cell">Name</th>
                        <th className="table-header-cell">Price</th>
                        <th className="table-header-cell">Change</th>
                        <th className="table-header-cell">Volume</th>
                        <th className="table-header-cell">Market Cap</th>
                        <th className="table-header-cell">RSI</th>
                        <th className="table-header-cell">Score</th>
                        <th className="table-header-cell">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="table-body">
                      {/* Results would be mapped here */}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
