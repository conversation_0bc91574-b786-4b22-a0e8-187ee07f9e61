import { useState, useCallback, useRef, useEffect } from 'react'
import toast from 'react-hot-toast'

export interface ApiError {
  status: number
  message: string
  details?: any
  timestamp?: number
  path?: string
}

export interface UseApiCallOptions {
  showSuccessToast?: boolean
  showErrorToast?: boolean
  successMessage?: string
  errorMessage?: string
  onSuccess?: (data: any) => void
  onError?: (error: ApiError) => void
  retryAttempts?: number
  retryDelay?: number
}

export interface UseApiCallResult<T> {
  data: T | null
  loading: boolean
  error: ApiError | null
  execute: (...args: any[]) => Promise<T | null>
  retry: () => Promise<T | null>
  reset: () => void
}

export function useApiCall<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiCallOptions = {}
): UseApiCallResult<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<ApiError | null>(null)
  
  const lastArgsRef = useRef<any[]>([])
  const retryCountRef = useRef(0)
  const abortControllerRef = useRef<AbortController | null>(null)
  
  const {
    showSuccessToast = false,
    showErrorToast = true,
    successMessage,
    errorMessage,
    onSuccess,
    onError,
    retryAttempts = 0,
    retryDelay = 1000
  } = options

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    
    abortControllerRef.current = new AbortController()
    lastArgsRef.current = args
    retryCountRef.current = 0
    
    setLoading(true)
    setError(null)
    
    try {
      const result = await apiFunction(...args)
      
      setData(result)
      setError(null)
      
      if (showSuccessToast) {
        toast.success(successMessage || 'Operation completed successfully')
      }
      
      if (onSuccess) {
        onSuccess(result)
      }
      
      return result
    } catch (err: any) {
      const apiError: ApiError = err.apiError || {
        status: err.response?.status || 0,
        message: err.message || 'An unexpected error occurred',
        details: err.response?.data
      }
      
      setError(apiError)
      setData(null)
      
      if (showErrorToast) {
        const message = errorMessage || apiError.message || 'An error occurred'
        toast.error(message)
      }
      
      if (onError) {
        onError(apiError)
      }
      
      return null
    } finally {
      setLoading(false)
      abortControllerRef.current = null
    }
  }, [apiFunction, showSuccessToast, showErrorToast, successMessage, errorMessage, onSuccess, onError])

  const retry = useCallback(async (): Promise<T | null> => {
    if (retryCountRef.current >= retryAttempts) {
      toast.error('Maximum retry attempts reached')
      return null
    }
    
    retryCountRef.current++
    
    // Add delay before retry
    if (retryDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, retryDelay))
    }
    
    return execute(...lastArgsRef.current)
  }, [execute, retryAttempts, retryDelay])

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
    retryCountRef.current = 0
    
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }
  }, [])

  return {
    data,
    loading,
    error,
    execute,
    retry,
    reset
  }
}

// Specialized hook for paginated API calls
export function usePaginatedApiCall<T = any>(
  apiFunction: (page: number, limit: number, ...args: any[]) => Promise<{ data: T[], total: number, page: number, limit: number }>,
  options: UseApiCallOptions & { initialPage?: number, initialLimit?: number } = {}
) {
  const { initialPage = 1, initialLimit = 20, ...apiOptions } = options
  
  const [page, setPage] = useState(initialPage)
  const [limit, setLimit] = useState(initialLimit)
  const [total, setTotal] = useState(0)
  
  const { data, loading, error, execute, retry, reset } = useApiCall(
    async (...args: any[]) => {
      const result = await apiFunction(page, limit, ...args)
      setTotal(result.total)
      return result.data
    },
    apiOptions
  )

  const loadPage = useCallback((newPage: number, ...args: any[]) => {
    setPage(newPage)
    return execute(...args)
  }, [execute])

  const changeLimit = useCallback((newLimit: number, ...args: any[]) => {
    setLimit(newLimit)
    setPage(1) // Reset to first page when changing limit
    return execute(...args)
  }, [execute])

  const nextPage = useCallback((...args: any[]) => {
    const totalPages = Math.ceil(total / limit)
    if (page < totalPages) {
      return loadPage(page + 1, ...args)
    }
    return Promise.resolve(null)
  }, [page, total, limit, loadPage])

  const prevPage = useCallback((...args: any[]) => {
    if (page > 1) {
      return loadPage(page - 1, ...args)
    }
    return Promise.resolve(null)
  }, [page, loadPage])

  const resetPagination = useCallback(() => {
    setPage(initialPage)
    setLimit(initialLimit)
    setTotal(0)
    reset()
  }, [initialPage, initialLimit, reset])

  return {
    data: data || [],
    loading,
    error,
    page,
    limit,
    total,
    totalPages: Math.ceil(total / limit),
    hasNextPage: page < Math.ceil(total / limit),
    hasPrevPage: page > 1,
    execute,
    retry,
    reset: resetPagination,
    loadPage,
    changeLimit,
    nextPage,
    prevPage
  }
}

// Hook for optimistic updates
export function useOptimisticApiCall<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  optimisticUpdate: (currentData: T | null, ...args: any[]) => T | null,
  options: UseApiCallOptions = {}
) {
  const [optimisticData, setOptimisticData] = useState<T | null>(null)
  
  const { data, loading, error, execute, retry, reset } = useApiCall(apiFunction, {
    ...options,
    onError: (error) => {
      // Revert optimistic update on error
      setOptimisticData(null)
      if (options.onError) {
        options.onError(error)
      }
    }
  })

  const executeOptimistic = useCallback(async (...args: any[]): Promise<T | null> => {
    // Apply optimistic update immediately
    const optimistic = optimisticUpdate(data, ...args)
    setOptimisticData(optimistic)
    
    const result = await execute(...args)
    
    // Clear optimistic data once real data is received
    setOptimisticData(null)
    
    return result
  }, [data, execute, optimisticUpdate])

  const resetOptimistic = useCallback(() => {
    setOptimisticData(null)
    reset()
  }, [reset])

  return {
    data: optimisticData || data,
    loading,
    error,
    execute: executeOptimistic,
    retry,
    reset: resetOptimistic,
    isOptimistic: optimisticData !== null
  }
}
