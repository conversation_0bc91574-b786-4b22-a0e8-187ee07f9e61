"""
Tests for stock API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.database import get_db, Base
from app.models.stock import Stock
from app.models.user import User
from app.core.security import get_password_hash, create_access_token

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_stocks.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


@pytest.fixture
def test_user():
    """Create a test user."""
    db = TestingSessionLocal()
    user = User(
        email="<EMAIL>",
        username="testuser",
        hashed_password=get_password_hash("testpass123"),
        first_name="Test",
        last_name="User",
        is_active=True
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    db.close()
    return user


@pytest.fixture
def auth_headers(test_user):
    """Create authentication headers."""
    token = create_access_token(data={"sub": test_user.username, "user_id": test_user.id})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def test_stock():
    """Create a test stock."""
    db = TestingSessionLocal()
    stock = Stock(
        symbol="AAPL",
        name="Apple Inc.",
        exchange="NASDAQ",
        sector="Technology",
        industry="Consumer Electronics",
        current_price=150.00,
        volume=1000000,
        market_cap=2500000000000,
        is_active=True,
        is_tradable=True
    )
    db.add(stock)
    db.commit()
    db.refresh(stock)
    db.close()
    return stock


class TestStockEndpoints:
    """Test stock API endpoints."""
    
    def test_get_stocks_unauthorized(self):
        """Test getting stocks without authentication."""
        response = client.get("/api/v1/stocks/")
        assert response.status_code == 401
    
    def test_get_stocks_success(self, auth_headers, test_stock):
        """Test getting stocks with authentication."""
        response = client.get("/api/v1/stocks/", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        assert data[0]["symbol"] == "AAPL"
    
    def test_get_stocks_with_filters(self, auth_headers, test_stock):
        """Test getting stocks with filters."""
        response = client.get(
            "/api/v1/stocks/?sector=Technology&search=Apple",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        assert data[0]["sector"] == "Technology"
    
    def test_get_stock_by_symbol_success(self, auth_headers, test_stock):
        """Test getting stock by symbol."""
        response = client.get("/api/v1/stocks/AAPL", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["symbol"] == "AAPL"
        assert data["name"] == "Apple Inc."
    
    def test_get_stock_by_symbol_not_found(self, auth_headers):
        """Test getting non-existent stock."""
        response = client.get("/api/v1/stocks/NONEXISTENT", headers=auth_headers)
        assert response.status_code == 404
    
    def test_search_stocks(self, auth_headers):
        """Test stock search endpoint."""
        response = client.get("/api/v1/stocks/search?q=AAPL", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_screen_stocks_basic(self, auth_headers, test_stock):
        """Test basic stock screening."""
        screening_criteria = {
            "min_price": 100,
            "max_price": 200,
            "sectors": ["Technology"]
        }
        response = client.post(
            "/api/v1/stocks/screen",
            json=screening_criteria,
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert "criteria" in data
        assert "results" in data
        assert "total_matches" in data
        assert "execution_time" in data
    
    def test_screen_stocks_with_technical_indicators(self, auth_headers, test_stock):
        """Test stock screening with technical indicators."""
        screening_criteria = {
            "min_price": 50,
            "max_price": 300,
            "rsi_min": 30,
            "rsi_max": 70,
            "volume_surge_threshold": 1.5
        }
        response = client.post(
            "/api/v1/stocks/screen",
            json=screening_criteria,
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["criteria"]["rsi_min"] == 30
        assert data["criteria"]["rsi_max"] == 70
    
    def test_screen_stocks_invalid_criteria(self, auth_headers):
        """Test stock screening with invalid criteria."""
        screening_criteria = {
            "min_price": 200,
            "max_price": 100  # Invalid: min > max
        }
        response = client.post(
            "/api/v1/stocks/screen",
            json=screening_criteria,
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
    
    def test_get_stock_history(self, auth_headers, test_stock):
        """Test getting stock historical data."""
        response = client.get(
            "/api/v1/stocks/AAPL/history?timeframe=1d&limit=30",
            headers=auth_headers
        )
        # This might return 503 if market data service is not available
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            data = response.json()
            assert data["symbol"] == "AAPL"
            assert data["timeframe"] == "1d"
            assert "data" in data
    
    def test_get_stock_history_invalid_timeframe(self, auth_headers, test_stock):
        """Test getting stock history with invalid timeframe."""
        response = client.get(
            "/api/v1/stocks/AAPL/history?timeframe=invalid",
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
    
    def test_pagination(self, auth_headers):
        """Test pagination parameters."""
        response = client.get(
            "/api/v1/stocks/?skip=0&limit=10",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data) <= 10
    
    def test_rate_limiting(self, auth_headers):
        """Test rate limiting (basic test)."""
        # This is a basic test - in production you'd test actual rate limits
        for _ in range(5):
            response = client.get("/api/v1/stocks/", headers=auth_headers)
            assert response.status_code == 200
    
    def test_error_handling(self, auth_headers):
        """Test error handling for various scenarios."""
        # Test with invalid stock symbol format
        response = client.get("/api/v1/stocks/INVALID_SYMBOL_TOO_LONG", headers=auth_headers)
        assert response.status_code == 404
        
        # Test with malformed request
        response = client.post(
            "/api/v1/stocks/screen",
            json={"invalid": "data"},
            headers=auth_headers
        )
        assert response.status_code in [422, 400]


class TestStockValidation:
    """Test stock data validation."""
    
    def test_stock_symbol_validation(self):
        """Test stock symbol validation."""
        from app.core.validation import input_sanitizer
        
        # Valid symbols
        assert input_sanitizer.sanitize_stock_symbol("AAPL") == "AAPL"
        assert input_sanitizer.sanitize_stock_symbol("msft") == "MSFT"
        
        # Invalid symbols
        with pytest.raises(Exception):
            input_sanitizer.sanitize_stock_symbol("INVALID_SYMBOL_TOO_LONG")
        
        with pytest.raises(Exception):
            input_sanitizer.sanitize_stock_symbol("INVALID-SYMBOL")
    
    def test_price_validation(self):
        """Test price validation."""
        from app.core.validation import input_sanitizer
        
        # Valid prices
        assert input_sanitizer.sanitize_price(100.50) == 100.50
        assert input_sanitizer.sanitize_price("150.75") == 150.75
        
        # Invalid prices
        with pytest.raises(Exception):
            input_sanitizer.sanitize_price(-10)
        
        with pytest.raises(Exception):
            input_sanitizer.sanitize_price("invalid")
    
    def test_percentage_validation(self):
        """Test percentage validation."""
        from app.core.validation import input_sanitizer
        
        # Valid percentages
        assert input_sanitizer.sanitize_percentage(50.5) == 50.5
        assert input_sanitizer.sanitize_percentage("75.25") == 75.25
        
        # Invalid percentages
        with pytest.raises(Exception):
            input_sanitizer.sanitize_percentage(150)  # > 100
        
        with pytest.raises(Exception):
            input_sanitizer.sanitize_percentage(-10)  # < 0


# Cleanup
def teardown_module():
    """Clean up test database."""
    Base.metadata.drop_all(bind=engine)
