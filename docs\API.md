# EntryAlert API Documentation

## Overview

The EntryAlert API is a RESTful API built with FastAPI that provides endpoints for stock screening, watchlist management, and real-time alerts.

**Base URL:** `http://localhost:8000/api/v1`

**Authentication:** <PERSON><PERSON> (JWT)

## Authentication

### Register User

**POST** `/auth/register`

Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "SecurePassword123",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "username",
  "full_name": "<PERSON>",
  "is_active": true,
  "is_verified": false,
  "is_premium": false,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Login

**POST** `/auth/login`

Authenticate user and receive access token.

**Request Body (Form Data):**
```
username: username
password: SecurePassword123
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": 1,
    "username": "username",
    "email": "<EMAIL>"
  }
}
```

### Get Current User

**GET** `/auth/me`

Get current authenticated user information.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "username",
  "full_name": "John Doe",
  "is_active": true,
  "timezone": "UTC",
  "notification_email": true,
  "notification_push": true
}
```

## Stocks

### Get Stocks

**GET** `/stocks/`

Get list of stocks with optional filtering.

**Query Parameters:**
- `skip` (int): Number of records to skip (default: 0)
- `limit` (int): Maximum number of records to return (default: 100)
- `sector` (string): Filter by sector
- `search` (string): Search by symbol or name

**Response:**
```json
[
  {
    "id": 1,
    "symbol": "AAPL",
    "name": "Apple Inc.",
    "exchange": "NASDAQ",
    "sector": "Technology",
    "current_price": 175.43,
    "price_change": 2.34,
    "price_change_percent": 1.35,
    "volume": 45200000,
    "market_cap": 2800000000000
  }
]
```

### Get Stock Detail

**GET** `/stocks/{symbol}`

Get detailed information for a specific stock.

**Response:**
```json
{
  "id": 1,
  "symbol": "AAPL",
  "name": "Apple Inc.",
  "exchange": "NASDAQ",
  "sector": "Technology",
  "industry": "Consumer Electronics",
  "current_price": 175.43,
  "previous_close": 173.09,
  "open_price": 174.20,
  "day_high": 176.80,
  "day_low": 173.50,
  "volume": 45200000,
  "avg_volume": 52000000,
  "market_cap": 2800000000000,
  "pe_ratio": 28.5,
  "eps": 6.15,
  "dividend_yield": 0.52,
  "beta": 1.2,
  "technical_indicators": {
    "rsi": 65.4,
    "macd": {
      "macd": 0.5,
      "signal": 0.3,
      "histogram": 0.2
    }
  }
}
```

### Get Stock History

**GET** `/stocks/{symbol}/history`

Get historical price data for a stock.

**Query Parameters:**
- `timeframe` (string): Time interval (1m, 5m, 15m, 1h, 1d)
- `limit` (int): Number of data points to return

**Response:**
```json
{
  "symbol": "AAPL",
  "timeframe": "1d",
  "data": [
    {
      "date": "2024-01-01T00:00:00Z",
      "open": 173.00,
      "high": 176.80,
      "low": 172.50,
      "close": 175.43,
      "volume": 45200000
    }
  ]
}
```

### Screen Stocks

**POST** `/stocks/screen`

Screen stocks based on specified criteria.

**Request Body:**
```json
{
  "min_price": 10.0,
  "max_price": 500.0,
  "min_volume": 1000000,
  "min_market_cap": **********,
  "sectors": ["Technology", "Healthcare"],
  "technical_filters": {
    "rsi_min": 30,
    "rsi_max": 70,
    "volume_surge_threshold": 2.0
  }
}
```

**Response:**
```json
{
  "criteria": { /* screening criteria */ },
  "results": [
    {
      "symbol": "AAPL",
      "name": "Apple Inc.",
      "current_price": 175.43,
      "score": 85.5,
      "signals": ["RSI Oversold", "Volume Surge"]
    }
  ],
  "total_matches": 25
}
```

## Watchlists

### Get User Watchlists

**GET** `/watchlists/`

Get all watchlists for the current user.

**Response:**
```json
[
  {
    "id": 1,
    "name": "Tech Stocks",
    "description": "Technology sector watchlist",
    "color": "#3B82F6",
    "stock_count": 5,
    "is_default": true,
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

### Create Watchlist

**POST** `/watchlists/`

Create a new watchlist.

**Request Body:**
```json
{
  "name": "Growth Stocks",
  "description": "High growth potential stocks",
  "color": "#10B981"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Growth Stocks",
  "description": "High growth potential stocks",
  "color": "#10B981",
  "stock_count": 0,
  "is_default": false,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Get Watchlist

**GET** `/watchlists/{watchlist_id}`

Get a specific watchlist with its items.

**Response:**
```json
{
  "id": 1,
  "name": "Tech Stocks",
  "description": "Technology sector watchlist",
  "items": [
    {
      "id": 1,
      "stock": {
        "symbol": "AAPL",
        "name": "Apple Inc.",
        "current_price": 175.43
      },
      "notes": "Strong buy signal",
      "target_price": 200.0,
      "price_alert_enabled": true
    }
  ]
}
```

### Update Watchlist

**PUT** `/watchlists/{watchlist_id}`

Update a watchlist.

**Request Body:**
```json
{
  "name": "Updated Tech Stocks",
  "description": "Updated description"
}
```

### Delete Watchlist

**DELETE** `/watchlists/{watchlist_id}`

Delete a watchlist.

**Response:**
```json
{
  "message": "Watchlist deleted successfully"
}
```

## Alerts

### Get User Alerts

**GET** `/alerts/`

Get alerts for the current user.

**Query Parameters:**
- `status_filter` (string): Filter by status (active, triggered, dismissed)
- `skip` (int): Number of records to skip
- `limit` (int): Maximum number of records to return

**Response:**
```json
[
  {
    "id": 1,
    "stock": {
      "symbol": "AAPL",
      "name": "Apple Inc."
    },
    "alert_type": "price_above",
    "title": "AAPL Price Alert",
    "message": "AAPL price crossed above $180",
    "status": "triggered",
    "priority": "medium",
    "triggered_at": "2024-01-01T12:00:00Z"
  }
]
```

### Create Alert

**POST** `/alerts/`

Create a new alert.

**Request Body:**
```json
{
  "stock_id": 1,
  "alert_type": "price_above",
  "title": "AAPL Price Alert",
  "message": "Alert when AAPL goes above $180",
  "conditions": {
    "target_price": 180.0
  },
  "priority": "medium",
  "email_notification": true,
  "push_notification": true
}
```

### Update Alert

**PUT** `/alerts/{alert_id}`

Update an alert.

### Delete Alert

**DELETE** `/alerts/{alert_id}`

Delete an alert.

## WebSocket

### Connection

**WebSocket** `/ws/{client_id}`

Establish WebSocket connection for real-time updates.

**Client ID Format:** `user_{user_id}_{timestamp}_{random}`

### Message Types

#### Subscribe to Stock Updates
```json
{
  "type": "subscribe",
  "topic": "stock:AAPL"
}
```

#### Stock Update (Server → Client)
```json
{
  "type": "stock_update",
  "data": {
    "symbol": "AAPL",
    "current_price": 175.43,
    "price_change": 2.34
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### Alert Notification (Server → Client)
```json
{
  "type": "alert",
  "data": {
    "id": 1,
    "title": "AAPL Price Alert",
    "message": "Price crossed above $180",
    "stock_symbol": "AAPL"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "error": "Invalid request data",
  "status_code": 400,
  "timestamp": 1704067200
}
```

### 401 Unauthorized
```json
{
  "error": "Could not validate credentials",
  "status_code": 401,
  "timestamp": 1704067200
}
```

### 404 Not Found
```json
{
  "error": "Resource not found",
  "status_code": 404,
  "timestamp": 1704067200
}
```

### 429 Too Many Requests
```json
{
  "error": "Rate limit exceeded",
  "status_code": 429,
  "timestamp": 1704067200
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "status_code": 500,
  "timestamp": 1704067200
}
```

## Rate Limiting

- Authentication endpoints: 10 requests per minute
- General API endpoints: 100 requests per minute
- WebSocket connections: 5 connections per user

## Pagination

List endpoints support pagination with the following parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 50, max: 1000)

Response includes pagination metadata:
```json
{
  "items": [...],
  "total": 1000,
  "page": 1,
  "size": 50,
  "pages": 20
}
```
