"""
Helper utility functions for the EntryAlert application.
"""
import re
import hashlib
import secrets
import string
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import json
from decimal import Decimal, ROUND_HALF_UP

from ..utils.logger import get_logger

logger = get_logger(__name__)


def validate_stock_symbol(symbol: str) -> bool:
    """
    Validate stock symbol format.
    
    Args:
        symbol: Stock symbol to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not symbol or not isinstance(symbol, str):
        return False
    
    # US stock symbols: 1-5 uppercase letters
    pattern = r'^[A-Z]{1,5}$'
    return bool(re.match(pattern, symbol.upper()))


def format_currency(amount: Union[float, Decimal], currency: str = "USD") -> str:
    """
    Format currency amount for display.
    
    Args:
        amount: Amount to format
        currency: Currency code
        
    Returns:
        Formatted currency string
    """
    if amount is None:
        return "N/A"
    
    try:
        if isinstance(amount, Decimal):
            amount = float(amount)
        
        if currency == "USD":
            return f"${amount:,.2f}"
        else:
            return f"{amount:,.2f} {currency}"
    except (ValueError, TypeError):
        return "N/A"


def format_percentage(value: Union[float, Decimal], decimal_places: int = 2) -> str:
    """
    Format percentage value for display.
    
    Args:
        value: Percentage value
        decimal_places: Number of decimal places
        
    Returns:
        Formatted percentage string
    """
    if value is None:
        return "N/A"
    
    try:
        if isinstance(value, Decimal):
            value = float(value)
        
        return f"{value:.{decimal_places}f}%"
    except (ValueError, TypeError):
        return "N/A"


def format_volume(volume: Union[int, float]) -> str:
    """
    Format volume for display with appropriate units.
    
    Args:
        volume: Volume value
        
    Returns:
        Formatted volume string
    """
    if volume is None:
        return "N/A"
    
    try:
        volume = int(volume)
        
        if volume >= 1_000_000_000:
            return f"{volume / 1_000_000_000:.1f}B"
        elif volume >= 1_000_000:
            return f"{volume / 1_000_000:.1f}M"
        elif volume >= 1_000:
            return f"{volume / 1_000:.1f}K"
        else:
            return str(volume)
    except (ValueError, TypeError):
        return "N/A"


def calculate_price_change(current_price: float, previous_price: float) -> Dict[str, float]:
    """
    Calculate price change and percentage change.
    
    Args:
        current_price: Current stock price
        previous_price: Previous stock price
        
    Returns:
        Dictionary with change and percentage_change
    """
    if not current_price or not previous_price:
        return {"change": 0.0, "percentage_change": 0.0}
    
    try:
        change = current_price - previous_price
        percentage_change = (change / previous_price) * 100
        
        return {
            "change": round(change, 2),
            "percentage_change": round(percentage_change, 2)
        }
    except (ValueError, TypeError, ZeroDivisionError):
        return {"change": 0.0, "percentage_change": 0.0}


def generate_random_string(length: int = 32) -> str:
    """
    Generate a random string for tokens, IDs, etc.
    
    Args:
        length: Length of the string
        
    Returns:
        Random string
    """
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def hash_string(text: str, salt: str = "") -> str:
    """
    Hash a string using SHA-256.
    
    Args:
        text: Text to hash
        salt: Optional salt
        
    Returns:
        Hashed string
    """
    return hashlib.sha256((text + salt).encode()).hexdigest()


def sanitize_input(text: str, max_length: int = 1000) -> str:
    """
    Sanitize user input by removing potentially harmful characters.
    
    Args:
        text: Input text to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized text
    """
    if not text or not isinstance(text, str):
        return ""
    
    # Remove HTML tags and scripts
    text = re.sub(r'<[^>]*>', '', text)
    
    # Remove potentially harmful characters
    text = re.sub(r'[<>"\']', '', text)
    
    # Limit length
    text = text[:max_length]
    
    return text.strip()


def parse_timeframe(timeframe: str) -> timedelta:
    """
    Parse timeframe string to timedelta object.
    
    Args:
        timeframe: Timeframe string (e.g., '1d', '1w', '1m', '1y')
        
    Returns:
        Timedelta object
    """
    timeframe_map = {
        '1d': timedelta(days=1),
        '1w': timedelta(weeks=1),
        '1m': timedelta(days=30),
        '3m': timedelta(days=90),
        '6m': timedelta(days=180),
        '1y': timedelta(days=365),
        '2y': timedelta(days=730),
        '5y': timedelta(days=1825)
    }
    
    return timeframe_map.get(timeframe.lower(), timedelta(days=1))


def round_decimal(value: Union[float, Decimal], decimal_places: int = 2) -> Decimal:
    """
    Round decimal value to specified decimal places.
    
    Args:
        value: Value to round
        decimal_places: Number of decimal places
        
    Returns:
        Rounded decimal value
    """
    if value is None:
        return Decimal('0')
    
    try:
        decimal_value = Decimal(str(value))
        return decimal_value.quantize(
            Decimal('0.1') ** decimal_places,
            rounding=ROUND_HALF_UP
        )
    except (ValueError, TypeError):
        return Decimal('0')


def is_market_hours() -> bool:
    """
    Check if current time is within market hours (9:30 AM - 4:00 PM ET).
    
    Returns:
        True if within market hours, False otherwise
    """
    try:
        from datetime import datetime
        import pytz
        
        # Get current time in Eastern timezone
        et_tz = pytz.timezone('US/Eastern')
        current_time = datetime.now(et_tz)
        
        # Check if it's a weekday
        if current_time.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
        
        # Market hours: 9:30 AM - 4:00 PM ET
        market_open = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = current_time.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_open <= current_time <= market_close
        
    except Exception as e:
        logger.error(f"Error checking market hours: {e}")
        return False


def calculate_market_cap(shares_outstanding: int, current_price: float) -> Optional[float]:
    """
    Calculate market capitalization.
    
    Args:
        shares_outstanding: Number of shares outstanding
        current_price: Current stock price
        
    Returns:
        Market capitalization or None if invalid inputs
    """
    if not shares_outstanding or not current_price:
        return None
    
    try:
        return shares_outstanding * current_price
    except (ValueError, TypeError):
        return None


def format_market_cap(market_cap: float) -> str:
    """
    Format market cap for display.
    
    Args:
        market_cap: Market capitalization value
        
    Returns:
        Formatted market cap string
    """
    if market_cap is None:
        return "N/A"
    
    try:
        if market_cap >= 1_000_000_000_000:  # Trillion
            return f"${market_cap / 1_000_000_000_000:.2f}T"
        elif market_cap >= 1_000_000_000:  # Billion
            return f"${market_cap / 1_000_000_000:.2f}B"
        elif market_cap >= 1_000_000:  # Million
            return f"${market_cap / 1_000_000:.2f}M"
        else:
            return f"${market_cap:,.0f}"
    except (ValueError, TypeError):
        return "N/A"


def validate_email(email: str) -> bool:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not email or not isinstance(email, str):
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    Safely load JSON string with fallback.
    
    Args:
        json_str: JSON string to parse
        default: Default value if parsing fails
        
    Returns:
        Parsed JSON or default value
    """
    if not json_str:
        return default
    
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """
    Safely dump object to JSON string with fallback.
    
    Args:
        obj: Object to serialize
        default: Default value if serialization fails
        
    Returns:
        JSON string or default value
    """
    try:
        return json.dumps(obj, default=str)
    except (TypeError, ValueError):
        return default


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split list into chunks of specified size.
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def get_business_days_between(start_date: datetime, end_date: datetime) -> int:
    """
    Calculate number of business days between two dates.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        Number of business days
    """
    try:
        import pandas as pd
        return len(pd.bdate_range(start_date, end_date))
    except Exception:
        # Fallback calculation
        days = (end_date - start_date).days
        weeks = days // 7
        remaining_days = days % 7
        
        # Approximate business days (5 days per week)
        return weeks * 5 + min(remaining_days, 5)
