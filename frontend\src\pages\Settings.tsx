import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, CreditCard } from 'lucide-react'

export const Settings: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-1">
          Manage your account and application preferences
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            <a href="#" className="bg-primary-50 text-primary-700 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
              <User className="text-primary-500 mr-3 h-5 w-5" />
              Profile
            </a>
            <a href="#" className="text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
              <Bell className="text-gray-400 mr-3 h-5 w-5" />
              Notifications
            </a>
            <a href="#" className="text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
              <Shield className="text-gray-400 mr-3 h-5 w-5" />
              Security
            </a>
            <a href="#" className="text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
              <CreditCard className="text-gray-400 mr-3 h-5 w-5" />
              Billing
            </a>
          </nav>
        </div>

        <div className="lg:col-span-3">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Profile Settings</h3>
            </div>
            <div className="card-body">
              <p className="text-gray-600">Profile settings would be displayed here</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
