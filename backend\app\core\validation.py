"""
Input validation and sanitization utilities.
"""
import re
import html
import bleach
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation

from .exceptions import ValidationError


class InputSanitizer:
    """Input sanitization utilities."""
    
    # Allowed HTML tags for rich text fields
    ALLOWED_TAGS = ['b', 'i', 'u', 'em', 'strong', 'p', 'br']
    ALLOWED_ATTRIBUTES = {}
    
    @staticmethod
    def sanitize_string(value: str, max_length: Optional[int] = None) -> str:
        """Sanitize string input."""
        if not isinstance(value, str):
            raise ValidationError("Value must be a string")
        
        # Remove null bytes and control characters
        value = value.replace('\x00', '').strip()
        
        # HTML escape
        value = html.escape(value)
        
        # Check length
        if max_length and len(value) > max_length:
            raise ValidationError(f"String too long (max {max_length} characters)")
        
        return value
    
    @staticmethod
    def sanitize_html(value: str, max_length: Optional[int] = None) -> str:
        """Sanitize HTML input allowing only safe tags."""
        if not isinstance(value, str):
            raise ValidationError("Value must be a string")
        
        # Clean HTML with bleach
        value = bleach.clean(
            value,
            tags=InputSanitizer.ALLOWED_TAGS,
            attributes=InputSanitizer.ALLOWED_ATTRIBUTES,
            strip=True
        )
        
        # Check length
        if max_length and len(value) > max_length:
            raise ValidationError(f"HTML content too long (max {max_length} characters)")
        
        return value
    
    @staticmethod
    def sanitize_email(email: str) -> str:
        """Sanitize and validate email address."""
        if not isinstance(email, str):
            raise ValidationError("Email must be a string")
        
        email = email.strip().lower()
        
        # Basic email regex
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValidationError("Invalid email format")
        
        return email
    
    @staticmethod
    def sanitize_username(username: str) -> str:
        """Sanitize and validate username."""
        if not isinstance(username, str):
            raise ValidationError("Username must be a string")
        
        username = username.strip()
        
        # Username validation: alphanumeric, underscore, hyphen
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            raise ValidationError("Username can only contain letters, numbers, underscore, and hyphen")
        
        if len(username) < 3:
            raise ValidationError("Username must be at least 3 characters")
        
        if len(username) > 50:
            raise ValidationError("Username must be less than 50 characters")
        
        return username
    
    @staticmethod
    def sanitize_stock_symbol(symbol: str) -> str:
        """Sanitize and validate stock symbol."""
        if not isinstance(symbol, str):
            raise ValidationError("Stock symbol must be a string")
        
        symbol = symbol.strip().upper()
        
        # Stock symbol validation: letters and numbers only
        if not re.match(r'^[A-Z0-9]+$', symbol):
            raise ValidationError("Stock symbol can only contain letters and numbers")
        
        if len(symbol) < 1 or len(symbol) > 10:
            raise ValidationError("Stock symbol must be 1-10 characters")
        
        return symbol
    
    @staticmethod
    def sanitize_number(value: Union[str, int, float], 
                       min_value: Optional[float] = None,
                       max_value: Optional[float] = None,
                       decimal_places: Optional[int] = None) -> float:
        """Sanitize and validate numeric input."""
        try:
            if isinstance(value, str):
                # Remove any non-numeric characters except decimal point and minus
                value = re.sub(r'[^\d.-]', '', value)
                value = float(value)
            elif isinstance(value, (int, float)):
                value = float(value)
            else:
                raise ValidationError("Value must be a number")
            
            # Check for NaN or infinity
            if not isinstance(value, (int, float)) or value != value:  # NaN check
                raise ValidationError("Invalid number")
            
            # Check range
            if min_value is not None and value < min_value:
                raise ValidationError(f"Value must be at least {min_value}")
            
            if max_value is not None and value > max_value:
                raise ValidationError(f"Value must be at most {max_value}")
            
            # Round to specified decimal places
            if decimal_places is not None:
                value = round(value, decimal_places)
            
            return value
            
        except (ValueError, TypeError, InvalidOperation):
            raise ValidationError("Invalid number format")
    
    @staticmethod
    def sanitize_percentage(value: Union[str, int, float]) -> float:
        """Sanitize and validate percentage (0-100)."""
        return InputSanitizer.sanitize_number(value, min_value=0, max_value=100, decimal_places=2)
    
    @staticmethod
    def sanitize_price(value: Union[str, int, float]) -> float:
        """Sanitize and validate price (positive number with 2 decimal places)."""
        return InputSanitizer.sanitize_number(value, min_value=0, decimal_places=2)
    
    @staticmethod
    def sanitize_datetime(value: Union[str, datetime], 
                         min_date: Optional[datetime] = None,
                         max_date: Optional[datetime] = None) -> datetime:
        """Sanitize and validate datetime input."""
        if isinstance(value, str):
            try:
                # Try ISO format first
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except ValueError:
                try:
                    # Try common formats
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%m/%d/%Y']:
                        try:
                            value = datetime.strptime(value, fmt)
                            break
                        except ValueError:
                            continue
                    else:
                        raise ValidationError("Invalid datetime format")
                except ValueError:
                    raise ValidationError("Invalid datetime format")
        
        elif not isinstance(value, datetime):
            raise ValidationError("Value must be a datetime")
        
        # Check range
        if min_date and value < min_date:
            raise ValidationError(f"Date must be after {min_date}")
        
        if max_date and value > max_date:
            raise ValidationError(f"Date must be before {max_date}")
        
        return value
    
    @staticmethod
    def sanitize_list(value: List[Any], 
                     max_items: Optional[int] = None,
                     item_validator: Optional[callable] = None) -> List[Any]:
        """Sanitize and validate list input."""
        if not isinstance(value, list):
            raise ValidationError("Value must be a list")
        
        if max_items and len(value) > max_items:
            raise ValidationError(f"List can have at most {max_items} items")
        
        if item_validator:
            try:
                value = [item_validator(item) for item in value]
            except Exception as e:
                raise ValidationError(f"Invalid list item: {str(e)}")
        
        return value
    
    @staticmethod
    def sanitize_dict(value: Dict[str, Any], 
                     allowed_keys: Optional[List[str]] = None,
                     required_keys: Optional[List[str]] = None) -> Dict[str, Any]:
        """Sanitize and validate dictionary input."""
        if not isinstance(value, dict):
            raise ValidationError("Value must be a dictionary")
        
        # Check allowed keys
        if allowed_keys:
            invalid_keys = set(value.keys()) - set(allowed_keys)
            if invalid_keys:
                raise ValidationError(f"Invalid keys: {list(invalid_keys)}")
        
        # Check required keys
        if required_keys:
            missing_keys = set(required_keys) - set(value.keys())
            if missing_keys:
                raise ValidationError(f"Missing required keys: {list(missing_keys)}")
        
        return value


class BusinessRuleValidator:
    """Business rule validation utilities."""
    
    @staticmethod
    def validate_stock_screening_criteria(criteria: Dict[str, Any]) -> Dict[str, Any]:
        """Validate stock screening criteria."""
        sanitizer = InputSanitizer()
        
        # Price range validation
        if 'min_price' in criteria and 'max_price' in criteria:
            min_price = criteria.get('min_price')
            max_price = criteria.get('max_price')
            if min_price is not None and max_price is not None and min_price > max_price:
                raise ValidationError("Minimum price cannot be greater than maximum price")
        
        # Market cap validation
        if 'min_market_cap' in criteria and 'max_market_cap' in criteria:
            min_cap = criteria.get('min_market_cap')
            max_cap = criteria.get('max_market_cap')
            if min_cap is not None and max_cap is not None and min_cap > max_cap:
                raise ValidationError("Minimum market cap cannot be greater than maximum market cap")
        
        # RSI validation
        if 'rsi_min' in criteria and 'rsi_max' in criteria:
            rsi_min = criteria.get('rsi_min')
            rsi_max = criteria.get('rsi_max')
            if rsi_min is not None and rsi_max is not None and rsi_min > rsi_max:
                raise ValidationError("Minimum RSI cannot be greater than maximum RSI")
        
        return criteria
    
    @staticmethod
    def validate_alert_conditions(alert_type: str, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Validate alert conditions based on alert type."""
        if alert_type == 'price_above' or alert_type == 'price_below':
            if 'target_price' not in conditions:
                raise ValidationError("Price alerts require target_price")
            
            target_price = conditions['target_price']
            if not isinstance(target_price, (int, float)) or target_price <= 0:
                raise ValidationError("Target price must be a positive number")
        
        elif alert_type == 'volume_surge':
            if 'volume_multiplier' not in conditions:
                raise ValidationError("Volume surge alerts require volume_multiplier")
            
            multiplier = conditions['volume_multiplier']
            if not isinstance(multiplier, (int, float)) or multiplier <= 1:
                raise ValidationError("Volume multiplier must be greater than 1")
        
        elif alert_type in ['rsi_oversold', 'rsi_overbought']:
            if 'rsi_level' not in conditions:
                raise ValidationError("RSI alerts require rsi_level")
            
            rsi_level = conditions['rsi_level']
            if not isinstance(rsi_level, (int, float)) or not 0 <= rsi_level <= 100:
                raise ValidationError("RSI level must be between 0 and 100")
        
        return conditions
    
    @staticmethod
    def validate_watchlist_limits(user_id: int, current_count: int, max_watchlists: int = 10) -> bool:
        """Validate watchlist creation limits."""
        if current_count >= max_watchlists:
            raise ValidationError(f"Maximum {max_watchlists} watchlists allowed per user")
        return True
    
    @staticmethod
    def validate_alert_limits(user_id: int, current_count: int, max_alerts: int = 100) -> bool:
        """Validate alert creation limits."""
        if current_count >= max_alerts:
            raise ValidationError(f"Maximum {max_alerts} alerts allowed per user")
        return True


# Global instances
input_sanitizer = InputSanitizer()
business_validator = BusinessRuleValidator()
