"""
Pydantic schemas for watchlist-related API endpoints.
"""
from pydantic import BaseModel, validator, Field
from typing import Optional, List
from datetime import datetime
from .stock import StockResponse


class WatchlistBase(BaseModel):
    """Base watchlist schema with common fields."""
    name: str = Field(..., min_length=1, max_length=100, description="Watchlist name")
    description: Optional[str] = Field(None, max_length=500, description="Watchlist description")
    is_public: bool = Field(False, description="Whether watchlist is public")
    
    @validator('name')
    def name_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Watchlist name cannot be empty')
        return v.strip()


class WatchlistCreate(WatchlistBase):
    """Schema for creating a new watchlist."""
    pass


class WatchlistUpdate(BaseModel):
    """Schema for updating watchlist information."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_public: Optional[bool] = None
    
    @validator('name')
    def name_not_empty(cls, v):
        if v is not None and not v.strip():
            raise ValueError('Watchlist name cannot be empty')
        return v.strip() if v else v


class WatchlistItemBase(BaseModel):
    """Base watchlist item schema."""
    stock_id: int = Field(..., gt=0, description="Stock ID")
    notes: Optional[str] = Field(None, max_length=1000, description="Personal notes about the stock")
    target_price: Optional[float] = Field(None, gt=0, description="Target price for the stock")
    stop_loss: Optional[float] = Field(None, gt=0, description="Stop loss price")
    
    @validator('target_price', 'stop_loss')
    def positive_prices(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Prices must be positive')
        return v


class WatchlistItemCreate(WatchlistItemBase):
    """Schema for adding a stock to watchlist."""
    pass


class WatchlistItemUpdate(BaseModel):
    """Schema for updating watchlist item."""
    notes: Optional[str] = Field(None, max_length=1000)
    target_price: Optional[float] = Field(None, gt=0)
    stop_loss: Optional[float] = Field(None, gt=0)
    
    @validator('target_price', 'stop_loss')
    def positive_prices(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Prices must be positive')
        return v


class WatchlistItemResponse(WatchlistItemBase):
    """Schema for watchlist item response."""
    id: int
    watchlist_id: int
    position: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    stock: Optional[StockResponse] = None
    
    class Config:
        from_attributes = True


class WatchlistResponse(WatchlistBase):
    """Schema for watchlist response data."""
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    items_count: int = 0
    items: Optional[List[WatchlistItemResponse]] = None
    
    class Config:
        from_attributes = True


class WatchlistSummary(BaseModel):
    """Schema for watchlist summary (without items)."""
    id: int
    name: str
    description: Optional[str] = None
    is_public: bool
    items_count: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class WatchlistStats(BaseModel):
    """Schema for watchlist statistics."""
    total_value: Optional[float] = Field(None, description="Total value of all stocks")
    total_change: Optional[float] = Field(None, description="Total change in value")
    total_change_percent: Optional[float] = Field(None, description="Total change percentage")
    best_performer: Optional[str] = Field(None, description="Best performing stock symbol")
    worst_performer: Optional[str] = Field(None, description="Worst performing stock symbol")
    avg_price_change: Optional[float] = Field(None, description="Average price change")
    
    class Config:
        schema_extra = {
            "example": {
                "total_value": 15000.50,
                "total_change": 250.75,
                "total_change_percent": 1.69,
                "best_performer": "AAPL",
                "worst_performer": "TSLA",
                "avg_price_change": 0.85
            }
        }


class WatchlistWithStats(WatchlistResponse):
    """Schema for watchlist with statistics."""
    stats: Optional[WatchlistStats] = None


class BulkWatchlistOperation(BaseModel):
    """Schema for bulk operations on watchlist items."""
    stock_ids: List[int] = Field(..., min_items=1, description="List of stock IDs")
    
    @validator('stock_ids')
    def unique_stock_ids(cls, v):
        if len(v) != len(set(v)):
            raise ValueError('Stock IDs must be unique')
        return v


class WatchlistImport(BaseModel):
    """Schema for importing watchlist from external source."""
    name: str = Field(..., min_length=1, max_length=100)
    symbols: List[str] = Field(..., min_items=1, max_items=100, description="List of stock symbols")
    description: Optional[str] = Field(None, max_length=500)
    
    @validator('symbols')
    def validate_symbols(cls, v):
        # Remove duplicates and validate format
        unique_symbols = list(set(symbol.upper().strip() for symbol in v))
        for symbol in unique_symbols:
            if not symbol.isalnum() or len(symbol) > 10:
                raise ValueError(f'Invalid symbol format: {symbol}')
        return unique_symbols


class WatchlistExport(BaseModel):
    """Schema for exporting watchlist data."""
    format: str = Field(..., regex="^(json|csv|xlsx)$", description="Export format")
    include_stats: bool = Field(True, description="Include statistics in export")
    include_notes: bool = Field(True, description="Include personal notes")
    
    class Config:
        schema_extra = {
            "example": {
                "format": "json",
                "include_stats": True,
                "include_notes": True
            }
        }


class WatchlistShare(BaseModel):
    """Schema for sharing watchlist."""
    share_type: str = Field(..., regex="^(public|private|link)$", description="Share type")
    expires_at: Optional[datetime] = Field(None, description="Expiration date for shared link")
    allow_comments: bool = Field(False, description="Allow comments on shared watchlist")
    
    @validator('expires_at')
    def expires_in_future(cls, v):
        if v is not None and v <= datetime.utcnow():
            raise ValueError('Expiration date must be in the future')
        return v
