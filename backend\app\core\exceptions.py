"""
Custom exceptions for the EntryAlert application.
"""
from fastapi import <PERSON>TTP<PERSON>x<PERSON>, status
from typing import Any, Dict, Optional


class EntryAlertException(Exception):
    """Base exception class for EntryAlert application."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(EntryAlertException):
    """Raised when input validation fails."""
    pass


class AuthenticationError(EntryAlertException):
    """Raised when authentication fails."""
    pass


class AuthorizationError(EntryAlertException):
    """Raised when user is not authorized to perform an action."""
    pass


class ResourceNotFoundError(EntryAlertException):
    """Base class for resource not found errors."""
    pass


class UserNotFoundError(ResourceNotFoundError):
    """Raised when a user is not found."""
    pass


class StockNotFoundError(ResourceNotFoundError):
    """Raised when a stock is not found."""
    pass


class WatchlistNotFoundError(ResourceNotFoundError):
    """Raised when a watchlist is not found."""
    pass


class AlertNotFoundError(ResourceNotFoundError):
    """Raised when an alert is not found."""
    pass


class MarketDataError(EntryAlertException):
    """Raised when market data operations fail."""
    pass


class RateLimitError(EntryAlertException):
    """Raised when API rate limits are exceeded."""
    pass


class ExternalAPIError(EntryAlertException):
    """Raised when external API calls fail."""
    pass


class DatabaseError(EntryAlertException):
    """Raised when database operations fail."""
    pass


class ConfigurationError(EntryAlertException):
    """Raised when configuration is invalid."""
    pass


class BusinessLogicError(EntryAlertException):
    """Raised when business logic validation fails."""
    pass


# HTTP Exception mappings
def map_exception_to_http(exception: EntryAlertException) -> HTTPException:
    """Map custom exceptions to HTTP exceptions."""
    
    exception_mapping = {
        ValidationError: (status.HTTP_400_BAD_REQUEST, "Validation Error"),
        AuthenticationError: (status.HTTP_401_UNAUTHORIZED, "Authentication Failed"),
        AuthorizationError: (status.HTTP_403_FORBIDDEN, "Access Denied"),
        ResourceNotFoundError: (status.HTTP_404_NOT_FOUND, "Resource Not Found"),
        UserNotFoundError: (status.HTTP_404_NOT_FOUND, "User Not Found"),
        StockNotFoundError: (status.HTTP_404_NOT_FOUND, "Stock Not Found"),
        WatchlistNotFoundError: (status.HTTP_404_NOT_FOUND, "Watchlist Not Found"),
        AlertNotFoundError: (status.HTTP_404_NOT_FOUND, "Alert Not Found"),
        MarketDataError: (status.HTTP_503_SERVICE_UNAVAILABLE, "Market Data Service Unavailable"),
        RateLimitError: (status.HTTP_429_TOO_MANY_REQUESTS, "Rate Limit Exceeded"),
        ExternalAPIError: (status.HTTP_502_BAD_GATEWAY, "External Service Error"),
        DatabaseError: (status.HTTP_500_INTERNAL_SERVER_ERROR, "Database Error"),
        ConfigurationError: (status.HTTP_500_INTERNAL_SERVER_ERROR, "Configuration Error"),
        BusinessLogicError: (status.HTTP_422_UNPROCESSABLE_ENTITY, "Business Logic Error"),
    }
    
    status_code, default_detail = exception_mapping.get(
        type(exception),
        (status.HTTP_500_INTERNAL_SERVER_ERROR, "Internal Server Error")
    )
    
    return HTTPException(
        status_code=status_code,
        detail={
            "error": exception.message,
            "type": type(exception).__name__,
            "details": exception.details
        }
    )


# Decorator for handling exceptions
def handle_exceptions(func):
    """Decorator to handle exceptions and convert them to HTTP exceptions."""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except EntryAlertException as e:
            raise map_exception_to_http(e)
        except Exception as e:
            # Log unexpected exceptions
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Unexpected error in {func.__name__}: {e}", exc_info=True)
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "An unexpected error occurred",
                    "type": "UnexpectedError"
                }
            )
    
    return wrapper


# Context manager for database operations
class DatabaseTransaction:
    """Context manager for database transactions with exception handling."""
    
    def __init__(self, db_session):
        self.db = db_session
    
    def __enter__(self):
        return self.db
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.db.rollback()
            if issubclass(exc_type, EntryAlertException):
                return False  # Re-raise the exception
            else:
                # Convert unexpected database errors
                raise DatabaseError(
                    "Database operation failed",
                    {"original_error": str(exc_val)}
                )
        else:
            try:
                self.db.commit()
            except Exception as e:
                self.db.rollback()
                raise DatabaseError(
                    "Failed to commit database transaction",
                    {"original_error": str(e)}
                )


# Validation helpers
def validate_positive_number(value: float, field_name: str) -> float:
    """Validate that a number is positive."""
    if value <= 0:
        raise ValidationError(f"{field_name} must be positive", {"value": value})
    return value


def validate_percentage(value: float, field_name: str) -> float:
    """Validate that a value is a valid percentage (0-100)."""
    if not 0 <= value <= 100:
        raise ValidationError(f"{field_name} must be between 0 and 100", {"value": value})
    return value


def validate_stock_symbol(symbol: str) -> str:
    """Validate stock symbol format."""
    if not symbol or not symbol.isalnum() or len(symbol) > 10:
        raise ValidationError("Invalid stock symbol format", {"symbol": symbol})
    return symbol.upper()


def validate_timeframe(timeframe: str) -> str:
    """Validate timeframe format."""
    valid_timeframes = ["1m", "5m", "15m", "1h", "1d", "1w", "1M"]
    if timeframe not in valid_timeframes:
        raise ValidationError(
            "Invalid timeframe",
            {"timeframe": timeframe, "valid_options": valid_timeframes}
        )
    return timeframe


# Rate limiting helpers
class RateLimitTracker:
    """Simple rate limit tracker."""
    
    def __init__(self):
        self.requests = {}
    
    def check_rate_limit(self, key: str, limit: int, window: int) -> bool:
        """Check if request is within rate limit."""
        import time
        
        current_time = time.time()
        if key not in self.requests:
            self.requests[key] = []
        
        # Remove old requests outside the window
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if current_time - req_time < window
        ]
        
        # Check if we're within the limit
        if len(self.requests[key]) >= limit:
            raise RateLimitError(
                f"Rate limit exceeded for {key}",
                {"limit": limit, "window": window, "current_count": len(self.requests[key])}
            )
        
        # Add current request
        self.requests[key].append(current_time)
        return True


# Global rate limit tracker
rate_limit_tracker = RateLimitTracker()
