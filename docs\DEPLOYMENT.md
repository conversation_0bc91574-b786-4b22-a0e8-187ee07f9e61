# EntryAlert Deployment Guide

This guide covers different deployment options for the EntryAlert application.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Environment Variables](#environment-variables)
- [Local Development](#local-development)
- [Docker Deployment](#docker-deployment)
- [Production Deployment](#production-deployment)
- [Database Setup](#database-setup)
- [Monitoring](#monitoring)
- [Troubleshooting](#troubleshooting)

## Prerequisites

- Docker and Docker Compose
- PostgreSQL 13+
- Redis 6+
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

## Environment Variables

Create a `.env` file in the backend directory with the following variables:

```env
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/entryalert
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
YAHOO_FINANCE_API_KEY=your_yahoo_finance_api_key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# Application Settings
DEBUG=False
ENVIRONMENT=production
ALLOWED_ORIGINS=https://yourdomain.com

# Background Tasks
SCREENING_INTERVAL_MINUTES=5
MARKET_DATA_UPDATE_INTERVAL_MINUTES=1
ALERT_CHECK_INTERVAL_MINUTES=1
```

## Local Development

### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Create and activate virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up database:
```bash
alembic upgrade head
```

5. Run the application:
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Docker Deployment

### Quick Start with Docker Compose

1. Clone the repository:
```bash
git clone https://github.com/HectorTa1989/EntryAlert.git
cd EntryAlert
```

2. Create environment file:
```bash
cp backend/.env.example backend/.env
# Edit the .env file with your configuration
```

3. Start all services:
```bash
docker-compose up -d
```

4. Run database migrations:
```bash
docker-compose exec backend alembic upgrade head
```

The application will be available at http://localhost:3000

### Individual Container Deployment

#### Backend Container

```bash
# Build the image
docker build -t entryalert-backend ./backend

# Run the container
docker run -d \
  --name entryalert-backend \
  -p 8000:8000 \
  --env-file backend/.env \
  entryalert-backend
```

#### Frontend Container

```bash
# Build the image
docker build -t entryalert-frontend ./frontend

# Run the container
docker run -d \
  --name entryalert-frontend \
  -p 3000:3000 \
  entryalert-frontend
```

## Production Deployment

### Using Docker Compose (Recommended)

1. Create production docker-compose file:

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: entryalert
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

  backend:
    image: your-registry/entryalert-backend:latest
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/entryalert
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=False
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  frontend:
    image: your-registry/entryalert-frontend:latest
    ports:
      - "80:3000"
    depends_on:
      - backend
    restart: unless-stopped

  worker:
    image: your-registry/entryalert-backend:latest
    command: python -m app.core.background_tasks
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/entryalert
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

2. Deploy:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Kubernetes Deployment

Example Kubernetes manifests are available in the `k8s/` directory:

```bash
kubectl apply -f k8s/
```

### Cloud Deployment Options

#### AWS ECS
- Use the provided task definitions in `aws/ecs/`
- Configure Application Load Balancer
- Set up RDS for PostgreSQL and ElastiCache for Redis

#### Google Cloud Run
- Deploy using the provided Cloud Build configuration
- Use Cloud SQL for PostgreSQL and Memorystore for Redis

#### Azure Container Instances
- Use the ARM templates in `azure/`
- Configure Azure Database for PostgreSQL and Azure Cache for Redis

## Database Setup

### Initial Migration

```bash
# Create migration
alembic revision --autogenerate -m "Initial migration"

# Apply migration
alembic upgrade head
```

### Backup and Restore

#### Backup
```bash
docker-compose exec postgres pg_dump -U postgres entryalert > backup.sql
```

#### Restore
```bash
docker-compose exec -T postgres psql -U postgres entryalert < backup.sql
```

## Monitoring

### Health Checks

The application provides health check endpoints:
- Backend: `GET /health`
- Frontend: `GET /` (returns 200 if healthy)

### Logging

Logs are structured in JSON format and can be collected using:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Fluentd
- Prometheus + Grafana

### Metrics

Key metrics to monitor:
- API response times
- Database connection pool usage
- WebSocket connection count
- Background task execution times
- Market data update success rate

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check database connectivity
docker-compose exec backend python -c "from app.database import engine; print(engine.execute('SELECT 1').scalar())"
```

#### Redis Connection Issues
```bash
# Check Redis connectivity
docker-compose exec backend python -c "from app.database import redis_client; print(redis_client.ping())"
```

#### WebSocket Connection Issues
- Check firewall settings for WebSocket ports
- Verify proxy configuration for WebSocket upgrades
- Check browser console for connection errors

#### Market Data Issues
- Verify API keys are correctly set
- Check rate limiting logs
- Monitor external API status

### Performance Optimization

#### Database
- Add indexes for frequently queried columns
- Use connection pooling
- Implement read replicas for heavy read workloads

#### Caching
- Increase Redis memory allocation
- Implement cache warming strategies
- Use CDN for static assets

#### Application
- Enable gzip compression
- Optimize database queries
- Use async/await properly
- Implement request rate limiting

### Security Considerations

- Use HTTPS in production
- Implement proper CORS policies
- Regularly update dependencies
- Use secrets management for sensitive data
- Enable database encryption at rest
- Implement proper authentication and authorization
- Regular security audits and penetration testing

## Support

For deployment issues:
1. Check the logs: `docker-compose logs -f`
2. Verify environment variables
3. Check database and Redis connectivity
4. Review the troubleshooting section
5. Open an issue on GitHub with detailed error information
