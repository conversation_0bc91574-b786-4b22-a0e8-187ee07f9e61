import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { ErrorBoundary, APIErrorBoundary, FormErrorBoundary } from '../ErrorBoundary'

// Mock component that throws an error
const ThrowError: React.FC<{ shouldThrow?: boolean }> = ({ shouldThrow = true }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// Mock console.error to avoid noise in tests
const originalError = console.error
beforeAll(() => {
  console.error = jest.fn()
})

afterAll(() => {
  console.error = originalError
})

describe('ErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )

    expect(screen.getByText('No error')).toBeInTheDocument()
  })

  it('renders error UI when there is an error', () => {
    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    )

    expect(screen.getByText('Oops! Something went wrong')).toBeInTheDocument()
    expect(screen.getByText('We\'re sorry, but something unexpected happened. Please try again.')).toBeInTheDocument()
  })

  it('shows error details in development mode', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'

    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    )

    expect(screen.getByText('Error Details (Development Only)')).toBeInTheDocument()
    expect(screen.getByText(/Test error/)).toBeInTheDocument()

    process.env.NODE_ENV = originalEnv
  })

  it('hides error details in production mode', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'production'

    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    )

    expect(screen.queryByText('Error Details (Development Only)')).not.toBeInTheDocument()

    process.env.NODE_ENV = originalEnv
  })

  it('calls onError callback when error occurs', () => {
    const onError = jest.fn()

    render(
      <ErrorBoundary onError={onError}>
        <ThrowError />
      </ErrorBoundary>
    )

    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String)
      })
    )
  })

  it('renders custom fallback when provided', () => {
    const customFallback = <div>Custom error message</div>

    render(
      <ErrorBoundary fallback={customFallback}>
        <ThrowError />
      </ErrorBoundary>
    )

    expect(screen.getByText('Custom error message')).toBeInTheDocument()
    expect(screen.queryByText('Oops! Something went wrong')).not.toBeInTheDocument()
  })

  it('resets error state when retry button is clicked', () => {
    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    )

    expect(screen.getByText('Oops! Something went wrong')).toBeInTheDocument()

    const retryButton = screen.getByText('Try Again')
    fireEvent.click(retryButton)

    // Re-render with non-throwing component
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )

    expect(screen.getByText('No error')).toBeInTheDocument()
  })

  it('navigates to home when go home button is clicked', () => {
    // Mock window.location
    const originalLocation = window.location
    delete (window as any).location
    window.location = { ...originalLocation, href: '' }

    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    )

    const homeButton = screen.getByText('Go Home')
    fireEvent.click(homeButton)

    expect(window.location.href).toBe('/')

    // Restore original location
    window.location = originalLocation
  })
})

describe('APIErrorBoundary', () => {
  it('renders API-specific error message', () => {
    render(
      <APIErrorBoundary>
        <ThrowError />
      </APIErrorBoundary>
    )

    expect(screen.getByText('Service Temporarily Unavailable')).toBeInTheDocument()
    expect(screen.getByText('We\'re experiencing some technical difficulties. Please try again in a moment.')).toBeInTheDocument()
  })

  it('has refresh page button', () => {
    // Mock window.location.reload
    const mockReload = jest.fn()
    Object.defineProperty(window.location, 'reload', {
      writable: true,
      value: mockReload,
    })

    render(
      <APIErrorBoundary>
        <ThrowError />
      </APIErrorBoundary>
    )

    const refreshButton = screen.getByText('Refresh Page')
    fireEvent.click(refreshButton)

    expect(mockReload).toHaveBeenCalled()
  })
})

describe('FormErrorBoundary', () => {
  it('renders form-specific error message', () => {
    render(
      <FormErrorBoundary>
        <ThrowError />
      </FormErrorBoundary>
    )

    expect(screen.getByText('Form Error')).toBeInTheDocument()
    expect(screen.getByText('There was an error with this form. Please refresh the page and try again.')).toBeInTheDocument()
  })
})

describe('withErrorBoundary HOC', () => {
  it('wraps component with error boundary', () => {
    const { withErrorBoundary } = require('../ErrorBoundary')
    
    const TestComponent = () => <div>Test Component</div>
    const WrappedComponent = withErrorBoundary(TestComponent)

    render(<WrappedComponent />)

    expect(screen.getByText('Test Component')).toBeInTheDocument()
  })

  it('catches errors in wrapped component', () => {
    const { withErrorBoundary } = require('../ErrorBoundary')
    
    const WrappedComponent = withErrorBoundary(ThrowError)

    render(<WrappedComponent />)

    expect(screen.getByText('Oops! Something went wrong')).toBeInTheDocument()
  })

  it('uses custom fallback and error handler', () => {
    const { withErrorBoundary } = require('../ErrorBoundary')
    
    const customFallback = <div>Custom HOC fallback</div>
    const onError = jest.fn()
    
    const WrappedComponent = withErrorBoundary(ThrowError, customFallback, onError)

    render(<WrappedComponent />)

    expect(screen.getByText('Custom HOC fallback')).toBeInTheDocument()
    expect(onError).toHaveBeenCalled()
  })

  it('sets correct display name', () => {
    const { withErrorBoundary } = require('../ErrorBoundary')
    
    const TestComponent = () => <div>Test</div>
    TestComponent.displayName = 'TestComponent'
    
    const WrappedComponent = withErrorBoundary(TestComponent)

    expect(WrappedComponent.displayName).toBe('withErrorBoundary(TestComponent)')
  })
})

describe('useErrorHandler hook', () => {
  it('logs errors to console', () => {
    const { useErrorHandler } = require('../ErrorBoundary')
    
    const TestComponent = () => {
      const handleError = useErrorHandler()
      
      React.useEffect(() => {
        handleError(new Error('Hook test error'), { extra: 'info' })
      }, [handleError])
      
      return <div>Test</div>
    }

    render(<TestComponent />)

    expect(console.error).toHaveBeenCalledWith('Error caught by hook:', expect.any(Error))
    expect(console.error).toHaveBeenCalledWith('Error info:', { extra: 'info' })
  })
})
