"""
Alert models for notifications and entry point detection.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from ..database import Base


class AlertType(enum.Enum):
    """Types of alerts that can be generated."""
    PRICE_ABOVE = "price_above"
    PRICE_BELOW = "price_below"
    VOLUME_SURGE = "volume_surge"
    RSI_OVERSOLD = "rsi_oversold"
    RSI_OVERBOUGHT = "rsi_overbought"
    MACD_BULLISH = "macd_bullish"
    MACD_BEARISH = "macd_bearish"
    BOLLINGER_SQUEEZE = "bollinger_squeeze"
    SUPPORT_BREAK = "support_break"
    RESISTANCE_BREAK = "resistance_break"
    MOMENTUM_CHANGE = "momentum_change"
    ENTRY_POINT = "entry_point"
    CUSTOM = "custom"


class AlertStatus(enum.Enum):
    """Status of alerts."""
    ACTIVE = "active"
    TRIGGERED = "triggered"
    DISMISSED = "dismissed"
    EXPIRED = "expired"


class AlertPriority(enum.Enum):
    """Priority levels for alerts."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Alert(Base):
    """Alert model for stock notifications."""
    
    __tablename__ = "alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False)
    
    # Alert configuration
    alert_type = Column(Enum(AlertType), nullable=False)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    
    # Alert conditions (stored as JSON for flexibility)
    conditions = Column(JSON, nullable=False)
    
    # Alert settings
    priority = Column(Enum(AlertPriority), default=AlertPriority.MEDIUM)
    status = Column(Enum(AlertStatus), default=AlertStatus.ACTIVE)
    
    # Notification settings
    email_notification = Column(Boolean, default=True)
    push_notification = Column(Boolean, default=True)
    
    # Trigger information
    trigger_price = Column(Float, nullable=True)
    trigger_value = Column(Float, nullable=True)
    trigger_data = Column(JSON, nullable=True)  # Additional trigger context
    
    # Expiration
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    triggered_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="alerts")
    stock = relationship("Stock", back_populates="alerts")
    notifications = relationship("AlertNotification", back_populates="alert", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Alert(id={self.id}, type={self.alert_type}, status={self.status})>"
    
    def to_dict(self):
        """Convert alert to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "stock_id": self.stock_id,
            "alert_type": self.alert_type.value if self.alert_type else None,
            "title": self.title,
            "message": self.message,
            "conditions": self.conditions,
            "priority": self.priority.value if self.priority else None,
            "status": self.status.value if self.status else None,
            "email_notification": self.email_notification,
            "push_notification": self.push_notification,
            "trigger_price": self.trigger_price,
            "trigger_value": self.trigger_value,
            "trigger_data": self.trigger_data,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "triggered_at": self.triggered_at.isoformat() if self.triggered_at else None,
        }


class AlertNotification(Base):
    """Notification delivery tracking for alerts."""
    
    __tablename__ = "alert_notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(Integer, ForeignKey("alerts.id"), nullable=False)
    
    # Notification details
    notification_type = Column(String(20), nullable=False)  # email, push, websocket
    recipient = Column(String(255), nullable=False)  # email address, device token, etc.
    
    # Delivery status
    status = Column(String(20), default="pending")  # pending, sent, delivered, failed
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    sent_at = Column(DateTime(timezone=True), nullable=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    alert = relationship("Alert", back_populates="notifications")
    
    def __repr__(self):
        return f"<AlertNotification(id={self.id}, type={self.notification_type}, status={self.status})>"


class ScreeningResult(Base):
    """Results from stock screening algorithms."""
    
    __tablename__ = "screening_results"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False)
    
    # Screening information
    screening_criteria = Column(JSON, nullable=False)
    score = Column(Float, nullable=False)  # Overall screening score
    
    # Technical analysis results
    technical_signals = Column(JSON, nullable=True)
    entry_points = Column(JSON, nullable=True)
    
    # Risk assessment
    risk_level = Column(String(20), default="medium")  # low, medium, high
    confidence = Column(Float, nullable=True)  # 0.0 to 1.0
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    stock = relationship("Stock")
    
    def __repr__(self):
        return f"<ScreeningResult(stock_id={self.stock_id}, score={self.score})>"
