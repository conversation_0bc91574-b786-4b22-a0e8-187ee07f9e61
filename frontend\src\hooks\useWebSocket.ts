import { useEffect, useState, useCallback, useRef } from 'react'
import { websocketService } from '@/services/websocketService'
import { WebSocketMessage } from '@/types'

interface UseWebSocketOptions {
  autoConnect?: boolean
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Event) => void
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const { autoConnect = true, onConnect, onDisconnect, onError } = options
  const [isConnected, setIsConnected] = useState(false)
  const [connectionState, setConnectionState] = useState<string>('CLOSED')
  const [error, setError] = useState<Event | null>(null)
  const cleanupFunctions = useRef<(() => void)[]>([])

  const connect = useCallback(async () => {
    try {
      await websocketService.connect()
      setIsConnected(true)
      setError(null)
    } catch (err) {
      setError(err as Event)
      setIsConnected(false)
    }
  }, [])

  const disconnect = useCallback(() => {
    websocketService.disconnect()
    setIsConnected(false)
  }, [])

  const send = useCallback((message: any) => {
    websocketService.send(message)
  }, [])

  const subscribe = useCallback((messageType: string, handler: (message: WebSocketMessage) => void) => {
    return websocketService.subscribe(messageType, handler)
  }, [])

  useEffect(() => {
    // Set up connection handlers
    const unsubscribeConnect = websocketService.onConnect(() => {
      setIsConnected(true)
      setConnectionState('OPEN')
      onConnect?.()
    })

    const unsubscribeDisconnect = websocketService.onDisconnect(() => {
      setIsConnected(false)
      setConnectionState('CLOSED')
      onDisconnect?.()
    })

    const unsubscribeError = websocketService.onError((error) => {
      setError(error)
      setIsConnected(false)
      onError?.(error)
    })

    cleanupFunctions.current.push(unsubscribeConnect, unsubscribeDisconnect, unsubscribeError)

    // Auto-connect if enabled
    if (autoConnect) {
      connect()
    }

    // Update connection state periodically
    const stateInterval = setInterval(() => {
      setConnectionState(websocketService.getConnectionState())
    }, 1000)

    return () => {
      clearInterval(stateInterval)
      cleanupFunctions.current.forEach(cleanup => cleanup())
      cleanupFunctions.current = []
    }
  }, [autoConnect, connect, onConnect, onDisconnect, onError])

  return {
    isConnected,
    connectionState,
    error,
    connect,
    disconnect,
    send,
    subscribe,
  }
}

export const useStockUpdates = (symbols: string[] = []) => {
  const [stockData, setStockData] = useState<Record<string, any>>({})
  const { isConnected, subscribe } = useWebSocket()

  useEffect(() => {
    if (!isConnected) return

    // Subscribe to stock updates
    const unsubscribe = subscribe('stock_update', (message) => {
      const stock = message.data
      if (stock && stock.symbol) {
        setStockData(prev => ({
          ...prev,
          [stock.symbol]: stock
        }))
      }
    })

    // Subscribe to specific stocks
    symbols.forEach(symbol => {
      websocketService.subscribeToStock(symbol)
    })

    return () => {
      unsubscribe()
      symbols.forEach(symbol => {
        websocketService.unsubscribeFromStock(symbol)
      })
    }
  }, [isConnected, symbols, subscribe])

  return stockData
}

export const useAlerts = () => {
  const [alerts, setAlerts] = useState<any[]>([])
  const { subscribe } = useWebSocket()

  useEffect(() => {
    const unsubscribe = subscribe('alert', (message) => {
      const alert = message.data
      setAlerts(prev => [alert, ...prev].slice(0, 50)) // Keep last 50 alerts
    })

    // Listen for custom alert events
    const handleAlertReceived = (event: CustomEvent) => {
      setAlerts(prev => [event.detail, ...prev].slice(0, 50))
    }

    window.addEventListener('alertReceived', handleAlertReceived as EventListener)

    return () => {
      unsubscribe()
      window.removeEventListener('alertReceived', handleAlertReceived as EventListener)
    }
  }, [subscribe])

  const clearAlerts = useCallback(() => {
    setAlerts([])
  }, [])

  const removeAlert = useCallback((alertId: number) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId))
  }, [])

  return {
    alerts,
    clearAlerts,
    removeAlert,
  }
}

export const useWatchlistUpdates = () => {
  const [watchlistUpdates, setWatchlistUpdates] = useState<any[]>([])
  const { subscribe } = useWebSocket()

  useEffect(() => {
    const unsubscribe = subscribe('watchlist_update', (message) => {
      const update = message.data
      setWatchlistUpdates(prev => [update, ...prev].slice(0, 10))
    })

    // Listen for custom watchlist events
    const handleWatchlistUpdate = (event: CustomEvent) => {
      setWatchlistUpdates(prev => [event.detail, ...prev].slice(0, 10))
    }

    window.addEventListener('watchlistUpdate', handleWatchlistUpdate as EventListener)

    return () => {
      unsubscribe()
      window.removeEventListener('watchlistUpdate', handleWatchlistUpdate as EventListener)
    }
  }, [subscribe])

  return watchlistUpdates
}
