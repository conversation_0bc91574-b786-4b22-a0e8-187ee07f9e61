"""
Alert API endpoints.
"""
from fastapi import API<PERSON>outer, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..models.user import User
from ..models.alert import <PERSON><PERSON>, AlertStatus
from ..core.security import get_current_active_user
from ..utils.logger import log_api_call

router = APIRouter()


@router.get("/")
async def get_user_alerts(
    status_filter: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get alerts for the current user."""
    log_api_call("/alerts", "GET", user_id=current_user.id)
    
    query = db.query(Alert).filter(Alert.user_id == current_user.id)
    
    if status_filter:
        try:
            status_enum = AlertStatus(status_filter)
            query = query.filter(Alert.status == status_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid status filter"
            )
    
    alerts = query.order_by(Alert.created_at.desc()).offset(skip).limit(limit).all()
    return [alert.to_dict() for alert in alerts]


@router.post("/")
async def create_alert(
    alert_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new alert."""
    log_api_call("/alerts", "POST", user_id=current_user.id)
    
    # This would implement alert creation logic
    # For now, return placeholder
    return {
        "message": "Alert created successfully",
        "alert_id": 1
    }


@router.get("/{alert_id}")
async def get_alert(
    alert_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific alert."""
    log_api_call(f"/alerts/{alert_id}", "GET", user_id=current_user.id)
    
    alert = db.query(Alert).filter(
        Alert.id == alert_id,
        Alert.user_id == current_user.id
    ).first()
    
    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )
    
    return alert.to_dict()


@router.put("/{alert_id}")
async def update_alert(
    alert_id: int,
    alert_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update an alert."""
    log_api_call(f"/alerts/{alert_id}", "PUT", user_id=current_user.id)
    
    alert = db.query(Alert).filter(
        Alert.id == alert_id,
        Alert.user_id == current_user.id
    ).first()
    
    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )
    
    # Update logic would go here
    return alert.to_dict()


@router.delete("/{alert_id}")
async def delete_alert(
    alert_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete an alert."""
    log_api_call(f"/alerts/{alert_id}", "DELETE", user_id=current_user.id)
    
    alert = db.query(Alert).filter(
        Alert.id == alert_id,
        Alert.user_id == current_user.id
    ).first()
    
    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Alert not found"
        )
    
    db.delete(alert)
    db.commit()
    
    return {"message": "Alert deleted successfully"}
