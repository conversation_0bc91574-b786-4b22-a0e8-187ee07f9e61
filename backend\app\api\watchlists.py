"""
Watchlist API endpoints.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from ..database import get_db
from ..models.user import User
from ..models.watchlist import Watchlist, WatchlistItem
from ..core.security import get_current_active_user
from ..utils.logger import log_api_call

router = APIRouter()


@router.get("/")
async def get_user_watchlists(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all watchlists for the current user."""
    log_api_call("/watchlists", "GET", user_id=current_user.id)
    
    watchlists = db.query(Watchlist).filter(
        Watchlist.user_id == current_user.id
    ).order_by(Watchlist.sort_order, Watchlist.created_at).all()
    
    return [watchlist.to_dict() for watchlist in watchlists]


@router.post("/")
async def create_watchlist(
    watchlist_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new watchlist."""
    log_api_call("/watchlists", "POST", user_id=current_user.id)
    
    watchlist = Watchlist(
        user_id=current_user.id,
        name=watchlist_data.get("name"),
        description=watchlist_data.get("description"),
        color=watchlist_data.get("color", "#3B82F6")
    )
    
    db.add(watchlist)
    db.commit()
    db.refresh(watchlist)
    
    return watchlist.to_dict()


@router.get("/{watchlist_id}")
async def get_watchlist(
    watchlist_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific watchlist with its items."""
    log_api_call(f"/watchlists/{watchlist_id}", "GET", user_id=current_user.id)
    
    watchlist = db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    
    if not watchlist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Watchlist not found"
        )
    
    return watchlist.to_dict()


@router.put("/{watchlist_id}")
async def update_watchlist(
    watchlist_id: int,
    watchlist_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a watchlist."""
    log_api_call(f"/watchlists/{watchlist_id}", "PUT", user_id=current_user.id)
    
    watchlist = db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    
    if not watchlist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Watchlist not found"
        )
    
    # Update fields
    for field, value in watchlist_data.items():
        if hasattr(watchlist, field):
            setattr(watchlist, field, value)
    
    db.commit()
    db.refresh(watchlist)
    
    return watchlist.to_dict()


@router.delete("/{watchlist_id}")
async def delete_watchlist(
    watchlist_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a watchlist."""
    log_api_call(f"/watchlists/{watchlist_id}", "DELETE", user_id=current_user.id)
    
    watchlist = db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    
    if not watchlist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Watchlist not found"
        )
    
    db.delete(watchlist)
    db.commit()
    
    return {"message": "Watchlist deleted successfully"}
