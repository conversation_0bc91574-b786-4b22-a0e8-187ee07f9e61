"""
Logging configuration for EntryAlert application.
"""
import logging
import sys
from typing import Dict, Any
import structlog
from ..config import settings


def setup_logging():
    """Configure structured logging for the application."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.LOG_FORMAT == "json" else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)


def get_logger(name: str = None):
    """Get a structured logger instance."""
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to other classes."""
    
    @property
    def logger(self):
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_api_call(endpoint: str, method: str, user_id: int = None, **kwargs):
    """Log API call with structured data."""
    logger = get_logger("api")
    logger.info(
        "API call",
        endpoint=endpoint,
        method=method,
        user_id=user_id,
        **kwargs
    )


def log_market_data_update(symbol: str, source: str, success: bool, **kwargs):
    """Log market data update."""
    logger = get_logger("market_data")
    logger.info(
        "Market data update",
        symbol=symbol,
        source=source,
        success=success,
        **kwargs
    )


def log_alert_triggered(alert_id: int, user_id: int, stock_symbol: str, alert_type: str, **kwargs):
    """Log alert trigger event."""
    logger = get_logger("alerts")
    logger.info(
        "Alert triggered",
        alert_id=alert_id,
        user_id=user_id,
        stock_symbol=stock_symbol,
        alert_type=alert_type,
        **kwargs
    )


def log_screening_result(criteria: Dict[str, Any], results_count: int, execution_time: float, **kwargs):
    """Log stock screening results."""
    logger = get_logger("screening")
    logger.info(
        "Screening completed",
        criteria=criteria,
        results_count=results_count,
        execution_time=execution_time,
        **kwargs
    )
