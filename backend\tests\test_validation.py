"""
Tests for input validation and sanitization.
"""
import pytest
from datetime import datetime, timedelta

from app.core.validation import (
    InputSanitizer, BusinessRuleValidator, ValidationError
)


class TestInputSanitizer:
    """Test input sanitization utilities."""
    
    def test_sanitize_string_valid(self):
        """Test string sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Normal string
        result = sanitizer.sanitize_string("Hello World")
        assert result == "Hello World"
        
        # String with whitespace
        result = sanitizer.sanitize_string("  Hello World  ")
        assert result == "Hello World"
        
        # String with HTML characters
        result = sanitizer.sanitize_string("<script>alert('xss')</script>")
        assert "&lt;script&gt;" in result
        assert "&lt;/script&gt;" in result
    
    def test_sanitize_string_invalid(self):
        """Test string sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Non-string input
        with pytest.raises(ValidationError):
            sanitizer.sanitize_string(123)
        
        # String too long
        with pytest.raises(ValidationError):
            sanitizer.sanitize_string("x" * 1000, max_length=10)
    
    def test_sanitize_email_valid(self):
        """Test email sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid emails
        assert sanitizer.sanitize_email("<EMAIL>") == "<EMAIL>"
        assert sanitizer.sanitize_email("  <EMAIL>  ") == "<EMAIL>"
        assert sanitizer.sanitize_email("<EMAIL>") == "<EMAIL>"
    
    def test_sanitize_email_invalid(self):
        """Test email sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid emails
        with pytest.raises(ValidationError):
            sanitizer.sanitize_email("invalid-email")
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_email("@domain.com")
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_email("user@")
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_email(123)
    
    def test_sanitize_username_valid(self):
        """Test username sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid usernames
        assert sanitizer.sanitize_username("testuser") == "testuser"
        assert sanitizer.sanitize_username("test_user") == "test_user"
        assert sanitizer.sanitize_username("test-user") == "test-user"
        assert sanitizer.sanitize_username("user123") == "user123"
    
    def test_sanitize_username_invalid(self):
        """Test username sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid usernames
        with pytest.raises(ValidationError):
            sanitizer.sanitize_username("ab")  # Too short
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_username("x" * 51)  # Too long
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_username("user@domain")  # Invalid characters
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_username("user space")  # Spaces
    
    def test_sanitize_stock_symbol_valid(self):
        """Test stock symbol sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid symbols
        assert sanitizer.sanitize_stock_symbol("AAPL") == "AAPL"
        assert sanitizer.sanitize_stock_symbol("aapl") == "AAPL"
        assert sanitizer.sanitize_stock_symbol("MSFT") == "MSFT"
        assert sanitizer.sanitize_stock_symbol("BRK.A") == "BRKA"  # Dots removed
    
    def test_sanitize_stock_symbol_invalid(self):
        """Test stock symbol sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid symbols
        with pytest.raises(ValidationError):
            sanitizer.sanitize_stock_symbol("")  # Empty
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_stock_symbol("TOOLONGSYMBOL")  # Too long
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_stock_symbol("INVALID-SYMBOL")  # Invalid characters
    
    def test_sanitize_number_valid(self):
        """Test number sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid numbers
        assert sanitizer.sanitize_number(123.45) == 123.45
        assert sanitizer.sanitize_number("123.45") == 123.45
        assert sanitizer.sanitize_number(100) == 100.0
        assert sanitizer.sanitize_number("$123.45") == 123.45  # Currency symbol removed
        
        # With constraints
        assert sanitizer.sanitize_number(50, min_value=0, max_value=100) == 50
        assert sanitizer.sanitize_number(123.456, decimal_places=2) == 123.46
    
    def test_sanitize_number_invalid(self):
        """Test number sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid numbers
        with pytest.raises(ValidationError):
            sanitizer.sanitize_number("not a number")
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_number(-10, min_value=0)
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_number(150, max_value=100)
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_number(float('nan'))
    
    def test_sanitize_percentage_valid(self):
        """Test percentage sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid percentages
        assert sanitizer.sanitize_percentage(50) == 50.0
        assert sanitizer.sanitize_percentage("75.5") == 75.5
        assert sanitizer.sanitize_percentage(0) == 0.0
        assert sanitizer.sanitize_percentage(100) == 100.0
    
    def test_sanitize_percentage_invalid(self):
        """Test percentage sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid percentages
        with pytest.raises(ValidationError):
            sanitizer.sanitize_percentage(-10)
        
        with pytest.raises(ValidationError):
            sanitizer.sanitize_percentage(150)
    
    def test_sanitize_price_valid(self):
        """Test price sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid prices
        assert sanitizer.sanitize_price(123.45) == 123.45
        assert sanitizer.sanitize_price("100.50") == 100.50
        assert sanitizer.sanitize_price(0) == 0.0
    
    def test_sanitize_price_invalid(self):
        """Test price sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid prices
        with pytest.raises(ValidationError):
            sanitizer.sanitize_price(-10)
    
    def test_sanitize_datetime_valid(self):
        """Test datetime sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid datetimes
        now = datetime.now()
        assert sanitizer.sanitize_datetime(now) == now
        
        # ISO format string
        iso_string = "2024-01-01T12:00:00"
        result = sanitizer.sanitize_datetime(iso_string)
        assert isinstance(result, datetime)
        
        # With constraints
        future_date = datetime.now() + timedelta(days=1)
        result = sanitizer.sanitize_datetime(
            future_date,
            min_date=datetime.now()
        )
        assert result == future_date
    
    def test_sanitize_datetime_invalid(self):
        """Test datetime sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid datetime
        with pytest.raises(ValidationError):
            sanitizer.sanitize_datetime("invalid date")
        
        # Date in past when future required
        past_date = datetime.now() - timedelta(days=1)
        with pytest.raises(ValidationError):
            sanitizer.sanitize_datetime(
                past_date,
                min_date=datetime.now()
            )
    
    def test_sanitize_list_valid(self):
        """Test list sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid lists
        assert sanitizer.sanitize_list([1, 2, 3]) == [1, 2, 3]
        assert sanitizer.sanitize_list([]) == []
        
        # With item validator
        result = sanitizer.sanitize_list(
            ["aapl", "msft"],
            item_validator=lambda x: x.upper()
        )
        assert result == ["AAPL", "MSFT"]
    
    def test_sanitize_list_invalid(self):
        """Test list sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid list
        with pytest.raises(ValidationError):
            sanitizer.sanitize_list("not a list")
        
        # Too many items
        with pytest.raises(ValidationError):
            sanitizer.sanitize_list([1, 2, 3, 4, 5], max_items=3)
    
    def test_sanitize_dict_valid(self):
        """Test dictionary sanitization with valid input."""
        sanitizer = InputSanitizer()
        
        # Valid dict
        data = {"key1": "value1", "key2": "value2"}
        assert sanitizer.sanitize_dict(data) == data
        
        # With allowed keys
        result = sanitizer.sanitize_dict(
            {"key1": "value1"},
            allowed_keys=["key1", "key2"]
        )
        assert result == {"key1": "value1"}
        
        # With required keys
        result = sanitizer.sanitize_dict(
            {"key1": "value1", "key2": "value2"},
            required_keys=["key1"]
        )
        assert result == {"key1": "value1", "key2": "value2"}
    
    def test_sanitize_dict_invalid(self):
        """Test dictionary sanitization with invalid input."""
        sanitizer = InputSanitizer()
        
        # Invalid dict
        with pytest.raises(ValidationError):
            sanitizer.sanitize_dict("not a dict")
        
        # Invalid keys
        with pytest.raises(ValidationError):
            sanitizer.sanitize_dict(
                {"invalid_key": "value"},
                allowed_keys=["valid_key"]
            )
        
        # Missing required keys
        with pytest.raises(ValidationError):
            sanitizer.sanitize_dict(
                {"key1": "value1"},
                required_keys=["key1", "key2"]
            )


class TestBusinessRuleValidator:
    """Test business rule validation."""
    
    def test_validate_stock_screening_criteria_valid(self):
        """Test valid stock screening criteria."""
        validator = BusinessRuleValidator()
        
        # Valid criteria
        criteria = {
            "min_price": 10,
            "max_price": 100,
            "min_market_cap": 1000000,
            "max_market_cap": 10000000,
            "rsi_min": 30,
            "rsi_max": 70
        }
        
        result = validator.validate_stock_screening_criteria(criteria)
        assert result == criteria
    
    def test_validate_stock_screening_criteria_invalid(self):
        """Test invalid stock screening criteria."""
        validator = BusinessRuleValidator()
        
        # Invalid price range
        with pytest.raises(ValidationError):
            validator.validate_stock_screening_criteria({
                "min_price": 100,
                "max_price": 50
            })
        
        # Invalid market cap range
        with pytest.raises(ValidationError):
            validator.validate_stock_screening_criteria({
                "min_market_cap": 10000000,
                "max_market_cap": 1000000
            })
        
        # Invalid RSI range
        with pytest.raises(ValidationError):
            validator.validate_stock_screening_criteria({
                "rsi_min": 70,
                "rsi_max": 30
            })
    
    def test_validate_alert_conditions_price_alerts(self):
        """Test alert condition validation for price alerts."""
        validator = BusinessRuleValidator()
        
        # Valid price above alert
        conditions = {"target_price": 150.0}
        result = validator.validate_alert_conditions("price_above", conditions)
        assert result == conditions
        
        # Invalid price alert (missing target_price)
        with pytest.raises(ValidationError):
            validator.validate_alert_conditions("price_above", {})
        
        # Invalid price alert (negative price)
        with pytest.raises(ValidationError):
            validator.validate_alert_conditions("price_above", {"target_price": -10})
    
    def test_validate_alert_conditions_volume_surge(self):
        """Test alert condition validation for volume surge alerts."""
        validator = BusinessRuleValidator()
        
        # Valid volume surge alert
        conditions = {"volume_multiplier": 2.0}
        result = validator.validate_alert_conditions("volume_surge", conditions)
        assert result == conditions
        
        # Invalid volume surge alert (missing multiplier)
        with pytest.raises(ValidationError):
            validator.validate_alert_conditions("volume_surge", {})
        
        # Invalid volume surge alert (multiplier <= 1)
        with pytest.raises(ValidationError):
            validator.validate_alert_conditions("volume_surge", {"volume_multiplier": 0.5})
    
    def test_validate_alert_conditions_rsi_alerts(self):
        """Test alert condition validation for RSI alerts."""
        validator = BusinessRuleValidator()
        
        # Valid RSI alert
        conditions = {"rsi_level": 30}
        result = validator.validate_alert_conditions("rsi_oversold", conditions)
        assert result == conditions
        
        # Invalid RSI alert (missing level)
        with pytest.raises(ValidationError):
            validator.validate_alert_conditions("rsi_oversold", {})
        
        # Invalid RSI alert (level out of range)
        with pytest.raises(ValidationError):
            validator.validate_alert_conditions("rsi_oversold", {"rsi_level": 150})
    
    def test_validate_watchlist_limits(self):
        """Test watchlist limit validation."""
        validator = BusinessRuleValidator()
        
        # Within limits
        assert validator.validate_watchlist_limits(1, 5, 10) == True
        
        # At limit
        with pytest.raises(ValidationError):
            validator.validate_watchlist_limits(1, 10, 10)
    
    def test_validate_alert_limits(self):
        """Test alert limit validation."""
        validator = BusinessRuleValidator()
        
        # Within limits
        assert validator.validate_alert_limits(1, 50, 100) == True
        
        # At limit
        with pytest.raises(ValidationError):
            validator.validate_alert_limits(1, 100, 100)
