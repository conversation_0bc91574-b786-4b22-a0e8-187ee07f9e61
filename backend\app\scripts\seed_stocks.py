"""
Stock data seeding script for EntryAlert application.
Populates the database with real US stock data from major exchanges.
"""
import asyncio
import csv
import io
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
import yfinance as yf
import requests
from datetime import datetime, timezone

from ..database import SessionLocal, engine
from ..models.stock import Stock
from ..services.market_data_service import market_data_service
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Major US stock exchanges and their common symbols
MAJOR_EXCHANGES = {
    'NASDAQ': 'NASDAQ',
    'NYSE': 'NYSE',
    'AMEX': 'AMEX'
}

# S&P 500 companies (sample list - in production, fetch from reliable source)
SP500_SYMBOLS = [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 'BRK.B', 'UNH', 'JNJ',
    'JPM', 'V', 'PG', 'XOM', 'HD', 'CVX', 'MA', 'BAC', 'ABBV', 'PFE',
    'AVGO', 'KO', 'MRK', 'COST', 'DIS', 'WMT', 'DHR', 'VZ', 'PEP', 'ABT',
    'ADBE', 'TMO', 'CRM', 'ACN', 'LIN', 'MCD', 'CSCO', 'WFC', 'NEE', 'BMY',
    'TXN', 'PM', 'RTX', 'QCOM', 'HON', 'UPS', 'T', 'SPGI', 'LOW', 'AMGN',
    'IBM', 'ELV', 'CAT', 'GS', 'INTU', 'DE', 'BKNG', 'AXP', 'BLK', 'SYK',
    'MDT', 'GILD', 'TJX', 'ADP', 'VRTX', 'LRCX', 'AMT', 'CVS', 'SCHW', 'MU',
    'C', 'ZTS', 'CB', 'FIS', 'MMC', 'SO', 'DUK', 'BSX', 'AON', 'CL',
    'CME', 'USB', 'PLD', 'NSC', 'ITW', 'HCA', 'SHW', 'ICE', 'GD', 'FCX',
    'WM', 'EMR', 'PNC', 'F', 'GM', 'APD', 'COP', 'TGT', 'ECL', 'DG'
]

# NASDAQ 100 additional symbols (sample)
NASDAQ100_ADDITIONAL = [
    'NFLX', 'INTC', 'CMCSA', 'TMUS', 'AMAT', 'ISRG', 'PYPL', 'CHTR', 'MDB', 'TEAM',
    'DOCU', 'ZM', 'ROKU', 'PTON', 'SNOW', 'PLTR', 'COIN', 'RBLX', 'HOOD', 'RIVN'
]

# Popular ETFs
POPULAR_ETFS = [
    'SPY', 'QQQ', 'IWM', 'VTI', 'VOO', 'VEA', 'VWO', 'BND', 'AGG', 'GLD',
    'SLV', 'USO', 'XLF', 'XLK', 'XLE', 'XLV', 'XLI', 'XLP', 'XLU', 'XLRE'
]


class StockDataSeeder:
    """Class to handle stock data seeding operations."""
    
    def __init__(self):
        self.session: Optional[Session] = None
        self.processed_count = 0
        self.error_count = 0
        self.batch_size = 50
    
    def get_db_session(self) -> Session:
        """Get database session."""
        if not self.session:
            self.session = SessionLocal()
        return self.session
    
    def close_session(self):
        """Close database session."""
        if self.session:
            self.session.close()
            self.session = None
    
    async def fetch_stock_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch stock information using mock data for development."""
        # For development, use mock data to avoid rate limiting issues
        # In production, this would fetch from Yahoo Finance or Alpha Vantage
        return self.create_mock_stock_data(symbol)

    def create_mock_stock_data(self, symbol: str) -> Dict[str, Any]:
        """Create mock stock data for development purposes."""
        import random

        # Mock company data based on symbol
        mock_companies = {
            'AAPL': {'name': 'Apple Inc.', 'sector': 'Technology', 'industry': 'Consumer Electronics'},
            'MSFT': {'name': 'Microsoft Corporation', 'sector': 'Technology', 'industry': 'Software'},
            'GOOGL': {'name': 'Alphabet Inc.', 'sector': 'Technology', 'industry': 'Internet Services'},
            'AMZN': {'name': 'Amazon.com Inc.', 'sector': 'Consumer Discretionary', 'industry': 'E-commerce'},
            'TSLA': {'name': 'Tesla Inc.', 'sector': 'Consumer Discretionary', 'industry': 'Electric Vehicles'},
            'META': {'name': 'Meta Platforms Inc.', 'sector': 'Technology', 'industry': 'Social Media'},
            'NVDA': {'name': 'NVIDIA Corporation', 'sector': 'Technology', 'industry': 'Semiconductors'},
            'JPM': {'name': 'JPMorgan Chase & Co.', 'sector': 'Financial Services', 'industry': 'Banking'},
            'JNJ': {'name': 'Johnson & Johnson', 'sector': 'Healthcare', 'industry': 'Pharmaceuticals'},
            'V': {'name': 'Visa Inc.', 'sector': 'Financial Services', 'industry': 'Payment Processing'},
        }

        company_info = mock_companies.get(symbol, {
            'name': f'{symbol} Corporation',
            'sector': 'Technology',
            'industry': 'Software'
        })

        # Generate realistic mock data
        base_price = random.uniform(50, 500)

        return {
            'symbol': symbol.upper(),
            'name': company_info['name'],
            'exchange': 'NASDAQ',
            'sector': company_info['sector'],
            'industry': company_info['industry'],
            'market_cap': int(random.uniform(10_000_000_000, 3_000_000_000_000)),
            'current_price': round(base_price, 2),
            'previous_close': round(base_price * random.uniform(0.98, 1.02), 2),
            'day_high': round(base_price * random.uniform(1.01, 1.05), 2),
            'day_low': round(base_price * random.uniform(0.95, 0.99), 2),
            'volume': int(random.uniform(1_000_000, 100_000_000)),
            'avg_volume': int(random.uniform(5_000_000, 50_000_000)),
            'pe_ratio': round(random.uniform(15, 35), 2),
            'eps': round(random.uniform(1, 20), 2),
            'dividend_yield': round(random.uniform(0, 0.05), 4) if random.random() > 0.3 else None,
            'beta': round(random.uniform(0.5, 2.0), 2),
            'is_active': True,
            'is_tradable': True,
            'last_data_update': datetime.now(timezone.utc)
        }
    
    async def create_or_update_stock(self, stock_data: Dict[str, Any]) -> bool:
        """Create or update stock in database."""
        try:
            db = self.get_db_session()
            
            # Check if stock already exists
            existing_stock = db.query(Stock).filter(Stock.symbol == stock_data['symbol']).first()
            
            if existing_stock:
                # Update existing stock
                for key, value in stock_data.items():
                    if hasattr(existing_stock, key) and value is not None:
                        setattr(existing_stock, key, value)
                logger.info(f"Updated stock: {stock_data['symbol']}")
            else:
                # Create new stock
                new_stock = Stock(**stock_data)
                db.add(new_stock)
                logger.info(f"Created stock: {stock_data['symbol']}")
            
            db.commit()
            self.processed_count += 1
            return True
            
        except Exception as e:
            logger.error(f"Error creating/updating stock {stock_data['symbol']}: {e}")
            db.rollback()
            self.error_count += 1
            return False
    
    async def seed_stock_batch(self, symbols: List[str]) -> None:
        """Seed a batch of stocks."""
        logger.info(f"Processing batch of {len(symbols)} stocks...")
        
        tasks = []
        for symbol in symbols:
            task = self.fetch_stock_info(symbol)
            tasks.append(task)
        
        # Fetch stock data concurrently
        stock_data_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for symbol, stock_data in zip(symbols, stock_data_list):
            if isinstance(stock_data, dict) and stock_data:
                await self.create_or_update_stock(stock_data)
            elif isinstance(stock_data, Exception):
                logger.error(f"Exception for {symbol}: {stock_data}")
                self.error_count += 1
            else:
                logger.warning(f"No data for {symbol}")
                self.error_count += 1
    
    async def seed_major_stocks(self) -> None:
        """Seed major US stocks from S&P 500, NASDAQ 100, and popular ETFs."""
        logger.info("Starting to seed major US stocks...")
        
        # Combine all symbol lists
        all_symbols = list(set(SP500_SYMBOLS + NASDAQ100_ADDITIONAL + POPULAR_ETFS))
        logger.info(f"Total symbols to process: {len(all_symbols)}")
        
        # Process in batches to avoid overwhelming the API
        for i in range(0, len(all_symbols), self.batch_size):
            batch = all_symbols[i:i + self.batch_size]
            await self.seed_stock_batch(batch)
            
            # Add delay between batches to respect rate limits
            if i + self.batch_size < len(all_symbols):
                logger.info(f"Processed {i + len(batch)}/{len(all_symbols)} stocks. Waiting before next batch...")
                await asyncio.sleep(2)  # 2 second delay between batches
        
        logger.info(f"Stock seeding completed. Processed: {self.processed_count}, Errors: {self.error_count}")
    
    async def update_stock_prices(self) -> None:
        """Update current prices for all stocks in database."""
        logger.info("Starting stock price updates...")
        
        db = self.get_db_session()
        stocks = db.query(Stock).filter(Stock.is_active == True).all()
        
        symbols = [stock.symbol for stock in stocks]
        logger.info(f"Updating prices for {len(symbols)} stocks...")
        
        # Process in batches
        for i in range(0, len(symbols), self.batch_size):
            batch = symbols[i:i + self.batch_size]
            await self.update_price_batch(batch)
            
            if i + self.batch_size < len(symbols):
                await asyncio.sleep(1)  # 1 second delay between batches
        
        logger.info(f"Price update completed. Processed: {self.processed_count}, Errors: {self.error_count}")
    
    async def update_price_batch(self, symbols: List[str]) -> None:
        """Update prices for a batch of stocks."""
        try:
            quotes = await market_data_service.get_multiple_quotes(symbols)
            
            db = self.get_db_session()
            
            for symbol, quote_data in quotes.items():
                if quote_data:
                    stock = db.query(Stock).filter(Stock.symbol == symbol).first()
                    if stock:
                        stock.current_price = quote_data.get('current_price')
                        stock.previous_close = quote_data.get('previous_close')
                        stock.day_high = quote_data.get('day_high')
                        stock.day_low = quote_data.get('day_low')
                        stock.volume = quote_data.get('volume')
                        stock.last_data_update = datetime.now(timezone.utc)
                        self.processed_count += 1
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Error updating price batch: {e}")
            db.rollback()


async def main():
    """Main function to run stock seeding."""
    seeder = StockDataSeeder()
    
    try:
        # Seed major stocks
        await seeder.seed_major_stocks()
        
        # Update prices
        await seeder.update_stock_prices()
        
    except Exception as e:
        logger.error(f"Error in main seeding process: {e}")
    finally:
        seeder.close_session()


if __name__ == "__main__":
    asyncio.run(main())
