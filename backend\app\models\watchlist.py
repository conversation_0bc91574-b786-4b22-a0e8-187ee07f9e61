"""
Watchlist models for organizing and tracking stocks.
"""
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean, Text, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class Watchlist(Base):
    """Watchlist model for organizing stocks."""
    
    __tablename__ = "watchlists"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Watchlist information
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    color = Column(String(7), default="#3B82F6")  # Hex color code
    
    # Settings
    is_public = Column(Boolean, default=False)
    is_default = Column(Boolean, default=False)
    sort_order = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="watchlists")
    items = relationship("WatchlistItem", back_populates="watchlist", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Watchlist(id={self.id}, name='{self.name}', user_id={self.user_id})>"
    
    @property
    def stock_count(self):
        """Get number of stocks in watchlist."""
        return len(self.items)
    
    def to_dict(self):
        """Convert watchlist to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "name": self.name,
            "description": self.description,
            "color": self.color,
            "is_public": self.is_public,
            "is_default": self.is_default,
            "sort_order": self.sort_order,
            "stock_count": self.stock_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class WatchlistItem(Base):
    """Individual items within a watchlist."""
    
    __tablename__ = "watchlist_items"
    
    id = Column(Integer, primary_key=True, index=True)
    watchlist_id = Column(Integer, ForeignKey("watchlists.id"), nullable=False)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False)
    
    # Item-specific settings
    notes = Column(Text, nullable=True)
    target_price = Column(Float, nullable=True)
    stop_loss = Column(Float, nullable=True)
    position = Column(Integer, default=0)  # For custom ordering within watchlist
    
    # Alert settings for this specific item
    price_alert_enabled = Column(Boolean, default=False)
    volume_alert_enabled = Column(Boolean, default=False)
    technical_alert_enabled = Column(Boolean, default=False)
    
    # Price alert thresholds
    price_alert_above = Column(Float, nullable=True)
    price_alert_below = Column(Float, nullable=True)
    
    # Volume alert threshold (multiplier of average volume)
    volume_alert_threshold = Column(Float, default=2.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    watchlist = relationship("Watchlist", back_populates="items")
    stock = relationship("Stock", back_populates="watchlist_items")
    
    def __repr__(self):
        return f"<WatchlistItem(watchlist_id={self.watchlist_id}, stock_id={self.stock_id})>"
    
    def to_dict(self):
        """Convert watchlist item to dictionary."""
        return {
            "id": self.id,
            "watchlist_id": self.watchlist_id,
            "stock_id": self.stock_id,
            "notes": self.notes,
            "target_price": self.target_price,
            "stop_loss": self.stop_loss,
            "position": self.position,
            "price_alert_enabled": self.price_alert_enabled,
            "volume_alert_enabled": self.volume_alert_enabled,
            "technical_alert_enabled": self.technical_alert_enabled,
            "price_alert_above": self.price_alert_above,
            "price_alert_below": self.price_alert_below,
            "volume_alert_threshold": self.volume_alert_threshold,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
