import { apiClient } from './api'
import { User, LoginCredentials, RegisterData, AuthResponse } from '@/types'

export const authService = {
  /**
   * Login user with credentials
   */
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)
    
    return apiClient.post<AuthResponse>('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
  },

  /**
   * Register new user
   */
  register: async (data: RegisterData): Promise<User> => {
    return apiClient.post<User>('/auth/register', data)
  },

  /**
   * Get current user information
   */
  getCurrentUser: async (): Promise<User> => {
    return apiClient.get<User>('/auth/me')
  },

  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    return apiClient.post<void>('/auth/logout')
  },

  /**
   * Change user password
   */
  changePassword: async (data: {
    current_password: string
    new_password: string
  }): Promise<void> => {
    return apiClient.post<void>('/auth/change-password', data)
  },

  /**
   * Update user profile
   */
  updateProfile: async (data: Partial<User>): Promise<User> => {
    return apiClient.put<User>('/auth/me', data)
  },
}
