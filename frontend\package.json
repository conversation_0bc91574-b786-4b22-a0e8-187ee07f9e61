{"name": "entryalert-frontend", "version": "1.0.0", "description": "EntryAlert - US Stock Screener Frontend", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "react-query": "^3.39.3", "axios": "^1.6.2", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "framer-motion": "^10.16.5", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-virtualized": "^9.22.5", "socket.io-client": "^4.7.4", "zustand": "^4.4.7", "react-intersection-observer": "^9.5.3", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-table": "^7.7.17", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "postcss": "^8.4.31", "prettier": "^3.0.3", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vite-plugin-pwa": "^0.17.4"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}