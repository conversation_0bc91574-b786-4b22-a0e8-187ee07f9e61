"""
Database configuration and session management.
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import redis
from .config import settings

# SQLAlchemy setup
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=StaticPool if "sqlite" in settings.DATABASE_URL else None,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {},
    echo=settings.DEBUG
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for SQLAlchemy models
Base = declarative_base()

# Metadata for Alembic migrations
metadata = MetaData()

# Redis connection
redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)


def get_db():
    """
    Dependency to get database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis():
    """
    Dependency to get Redis client.
    """
    return redis_client


async def init_db():
    """
    Initialize database tables.
    """
    # Import all models to ensure they are registered with SQLAlchemy
    from .models import user, stock, watchlist, alert
    
    # Create all tables
    Base.metadata.create_all(bind=engine)


async def close_db():
    """
    Close database connections.
    """
    engine.dispose()
    redis_client.close()


# Database utilities
class DatabaseManager:
    """Database management utilities."""
    
    @staticmethod
    def create_tables():
        """Create all database tables."""
        Base.metadata.create_all(bind=engine)
    
    @staticmethod
    def drop_tables():
        """Drop all database tables."""
        Base.metadata.drop_all(bind=engine)
    
    @staticmethod
    def reset_database():
        """Reset database by dropping and recreating tables."""
        DatabaseManager.drop_tables()
        DatabaseManager.create_tables()


# Cache utilities
class CacheManager:
    """Redis cache management utilities."""
    
    def __init__(self, redis_client=redis_client):
        self.redis = redis_client
    
    async def get(self, key: str):
        """Get value from cache."""
        return self.redis.get(key)
    
    async def set(self, key: str, value: str, ttl: int = settings.CACHE_TTL_SECONDS):
        """Set value in cache with TTL."""
        return self.redis.setex(key, ttl, value)
    
    async def delete(self, key: str):
        """Delete key from cache."""
        return self.redis.delete(key)
    
    async def exists(self, key: str):
        """Check if key exists in cache."""
        return self.redis.exists(key)
    
    async def flush_all(self):
        """Flush all cache data."""
        return self.redis.flushall()
    
    def get_market_data_key(self, symbol: str, timeframe: str = "1d"):
        """Generate cache key for market data."""
        return f"market_data:{symbol}:{timeframe}"
    
    def get_screening_results_key(self, criteria_hash: str):
        """Generate cache key for screening results."""
        return f"screening_results:{criteria_hash}"
    
    def get_user_watchlist_key(self, user_id: int):
        """Generate cache key for user watchlist."""
        return f"user_watchlist:{user_id}"


# Global cache manager instance
cache_manager = CacheManager()
