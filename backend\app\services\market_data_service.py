"""
Market data service for fetching and processing stock data.
"""
import asyncio
import aiohttp
import yfinance as yf
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd

from ..config import settings, MARKET_DATA_APIS
from ..database import cache_manager
from ..utils.logger import get_logger, log_market_data_update

logger = get_logger(__name__)


class MarketDataService:
    """Service for fetching market data from various sources."""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limits = {
            'alpha_vantage': {'requests': 0, 'reset_time': datetime.now()},
            'yahoo_finance': {'requests': 0, 'reset_time': datetime.now()},
        }
    
    async def get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def close_session(self):
        """Close HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()
    
    def check_rate_limit(self, provider: str) -> bool:
        """Check if we can make a request to the provider."""
        now = datetime.now()
        rate_info = self.rate_limits.get(provider, {})
        
        # Reset counter if enough time has passed
        if now - rate_info.get('reset_time', now) > timedelta(minutes=1):
            self.rate_limits[provider] = {'requests': 0, 'reset_time': now}
            return True
        
        # Check if we're under the limit
        api_config = MARKET_DATA_APIS.get(provider, {})
        max_requests = api_config.get('rate_limit', 100)
        
        return rate_info.get('requests', 0) < max_requests
    
    def increment_rate_limit(self, provider: str):
        """Increment rate limit counter."""
        if provider in self.rate_limits:
            self.rate_limits[provider]['requests'] += 1
    
    async def get_stock_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current stock quote."""
        cache_key = cache_manager.get_market_data_key(symbol, "quote")
        
        # Try cache first
        cached_data = await cache_manager.get(cache_key)
        if cached_data:
            return eval(cached_data)  # Note: In production, use json.loads
        
        # Try Yahoo Finance first (free and reliable)
        try:
            quote_data = await self._fetch_yahoo_quote(symbol)
            if quote_data:
                await cache_manager.set(cache_key, str(quote_data), ttl=60)  # Cache for 1 minute
                log_market_data_update(symbol, "yahoo_finance", True)
                return quote_data
        except Exception as e:
            logger.error(f"Error fetching Yahoo Finance quote for {symbol}: {e}")
        
        # Fallback to Alpha Vantage
        try:
            quote_data = await self._fetch_alpha_vantage_quote(symbol)
            if quote_data:
                await cache_manager.set(cache_key, str(quote_data), ttl=60)
                log_market_data_update(symbol, "alpha_vantage", True)
                return quote_data
        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage quote for {symbol}: {e}")
        
        log_market_data_update(symbol, "all_providers", False)
        return None
    
    async def _fetch_yahoo_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch quote from Yahoo Finance."""
        if not self.check_rate_limit('yahoo_finance'):
            return None
        
        try:
            # Use yfinance library for Yahoo Finance data
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info or 'regularMarketPrice' not in info:
                return None
            
            self.increment_rate_limit('yahoo_finance')
            
            return {
                'symbol': symbol.upper(),
                'current_price': info.get('regularMarketPrice'),
                'previous_close': info.get('previousClose'),
                'open_price': info.get('regularMarketOpen'),
                'day_high': info.get('regularMarketDayHigh'),
                'day_low': info.get('regularMarketDayLow'),
                'volume': info.get('regularMarketVolume'),
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'eps': info.get('trailingEps'),
                'dividend_yield': info.get('dividendYield'),
                'beta': info.get('beta'),
                'sector': info.get('sector'),
                'industry': info.get('industry'),
                'name': info.get('longName', info.get('shortName')),
                'exchange': info.get('exchange'),
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"Yahoo Finance API error for {symbol}: {e}")
            return None
    
    async def _fetch_alpha_vantage_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch quote from Alpha Vantage."""
        if not settings.ALPHA_VANTAGE_API_KEY or not self.check_rate_limit('alpha_vantage'):
            return None
        
        session = await self.get_session()
        url = MARKET_DATA_APIS['alpha_vantage']['base_url']
        
        params = {
            'function': 'GLOBAL_QUOTE',
            'symbol': symbol,
            'apikey': settings.ALPHA_VANTAGE_API_KEY
        }
        
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    quote = data.get('Global Quote', {})
                    
                    if not quote:
                        return None
                    
                    self.increment_rate_limit('alpha_vantage')
                    
                    return {
                        'symbol': symbol.upper(),
                        'current_price': float(quote.get('05. price', 0)),
                        'previous_close': float(quote.get('08. previous close', 0)),
                        'open_price': float(quote.get('02. open', 0)),
                        'day_high': float(quote.get('03. high', 0)),
                        'day_low': float(quote.get('04. low', 0)),
                        'volume': int(quote.get('06. volume', 0)),
                        'price_change': float(quote.get('09. change', 0)),
                        'price_change_percent': quote.get('10. change percent', '0%').replace('%', ''),
                        'timestamp': datetime.utcnow().isoformat()
                    }
        except Exception as e:
            logger.error(f"Alpha Vantage API error for {symbol}: {e}")
        
        return None
    
    async def get_historical_data(self, symbol: str, period: str = "1y") -> Optional[List[Dict[str, Any]]]:
        """Get historical price data."""
        cache_key = cache_manager.get_market_data_key(symbol, f"history_{period}")
        
        # Try cache first
        cached_data = await cache_manager.get(cache_key)
        if cached_data:
            return eval(cached_data)
        
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)
            
            if hist.empty:
                return None
            
            # Convert to list of dictionaries
            historical_data = []
            for date, row in hist.iterrows():
                historical_data.append({
                    'date': date.isoformat(),
                    'open': float(row['Open']),
                    'high': float(row['High']),
                    'low': float(row['Low']),
                    'close': float(row['Close']),
                    'volume': int(row['Volume']),
                    'adjusted_close': float(row['Close'])  # Yahoo Finance adjusts by default
                })
            
            # Cache for 1 hour
            await cache_manager.set(cache_key, str(historical_data), ttl=3600)
            return historical_data
            
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return None
    
    async def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get quotes for multiple symbols."""
        tasks = [self.get_stock_quote(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        quotes = {}
        for symbol, result in zip(symbols, results):
            if isinstance(result, dict):
                quotes[symbol] = result
            else:
                logger.error(f"Error fetching quote for {symbol}: {result}")
        
        return quotes
    
    async def search_stocks(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for stocks by symbol or name."""
        # This is a simplified implementation
        # In production, you'd use a proper search API
        try:
            # For now, just return some mock data
            # In reality, you'd search through a database of stocks
            # or use a search API like Alpha Vantage's SYMBOL_SEARCH
            return [
                {
                    'symbol': query.upper(),
                    'name': f'{query.upper()} Company',
                    'exchange': 'NASDAQ',
                    'type': 'Equity'
                }
            ]
        except Exception as e:
            logger.error(f"Error searching stocks for query '{query}': {e}")
            return []


# Global service instance
market_data_service = MarketDataService()
