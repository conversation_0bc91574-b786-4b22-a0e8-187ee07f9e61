"""
Pydantic schemas for stock-related API endpoints.
"""
from pydantic import BaseModel, validator, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal


class StockBase(BaseModel):
    """Base stock schema with common fields."""
    symbol: str = Field(..., min_length=1, max_length=10, description="Stock symbol")
    name: str = Field(..., min_length=1, max_length=200, description="Company name")
    exchange: str = Field(..., min_length=1, max_length=50, description="Stock exchange")
    sector: Optional[str] = Field(None, max_length=100, description="Industry sector")
    industry: Optional[str] = Field(None, max_length=100, description="Industry classification")
    
    @validator('symbol')
    def symbol_uppercase(cls, v):
        return v.upper().strip()
    
    @validator('name', 'exchange', 'sector', 'industry')
    def strip_strings(cls, v):
        return v.strip() if v else v


class StockCreate(StockBase):
    """Schema for creating a new stock."""
    market_cap: Optional[int] = Field(None, ge=0, description="Market capitalization")
    shares_outstanding: Optional[int] = Field(None, ge=0, description="Shares outstanding")


class StockUpdate(BaseModel):
    """Schema for updating stock information."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    sector: Optional[str] = Field(None, max_length=100)
    industry: Optional[str] = Field(None, max_length=100)
    market_cap: Optional[int] = Field(None, ge=0)
    shares_outstanding: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    is_tradable: Optional[bool] = None


class StockQuote(BaseModel):
    """Schema for current stock quote data."""
    symbol: str
    current_price: Optional[float] = Field(None, ge=0)
    previous_close: Optional[float] = Field(None, ge=0)
    open_price: Optional[float] = Field(None, ge=0)
    day_high: Optional[float] = Field(None, ge=0)
    day_low: Optional[float] = Field(None, ge=0)
    volume: Optional[int] = Field(None, ge=0)
    avg_volume: Optional[int] = Field(None, ge=0)
    price_change: Optional[float] = None
    price_change_percent: Optional[float] = None
    last_updated: Optional[datetime] = None


class StockTechnicalIndicators(BaseModel):
    """Schema for technical indicators."""
    rsi: Optional[float] = Field(None, ge=0, le=100, description="Relative Strength Index")
    macd: Optional[float] = Field(None, description="MACD value")
    macd_signal: Optional[float] = Field(None, description="MACD signal line")
    macd_histogram: Optional[float] = Field(None, description="MACD histogram")
    sma_20: Optional[float] = Field(None, ge=0, description="20-day Simple Moving Average")
    sma_50: Optional[float] = Field(None, ge=0, description="50-day Simple Moving Average")
    ema_12: Optional[float] = Field(None, ge=0, description="12-day Exponential Moving Average")
    ema_26: Optional[float] = Field(None, ge=0, description="26-day Exponential Moving Average")
    bollinger_upper: Optional[float] = Field(None, ge=0, description="Bollinger Band Upper")
    bollinger_lower: Optional[float] = Field(None, ge=0, description="Bollinger Band Lower")
    bollinger_middle: Optional[float] = Field(None, ge=0, description="Bollinger Band Middle")


class StockResponse(StockBase):
    """Schema for stock response data."""
    id: int
    current_price: Optional[float] = None
    previous_close: Optional[float] = None
    open_price: Optional[float] = None
    day_high: Optional[float] = None
    day_low: Optional[float] = None
    volume: Optional[int] = None
    avg_volume: Optional[int] = None
    price_change: Optional[float] = None
    price_change_percent: Optional[float] = None
    market_cap: Optional[int] = None
    shares_outstanding: Optional[int] = None
    pe_ratio: Optional[float] = None
    eps: Optional[float] = None
    dividend_yield: Optional[float] = None
    beta: Optional[float] = None
    technical_indicators: Optional[Dict[str, Any]] = None
    is_active: bool
    is_tradable: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_data_update: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class StockHistoryPoint(BaseModel):
    """Schema for a single historical price point."""
    timestamp: datetime
    open: float = Field(..., ge=0)
    high: float = Field(..., ge=0)
    low: float = Field(..., ge=0)
    close: float = Field(..., ge=0)
    volume: int = Field(..., ge=0)
    
    @validator('high')
    def high_gte_low(cls, v, values):
        if 'low' in values and v < values['low']:
            raise ValueError('High price must be >= low price')
        return v
    
    @validator('open', 'close')
    def price_within_range(cls, v, values):
        if 'low' in values and 'high' in values:
            if v < values['low'] or v > values['high']:
                raise ValueError('Open/Close price must be within high/low range')
        return v


class StockHistory(BaseModel):
    """Schema for historical stock data."""
    symbol: str
    timeframe: str = Field(..., pattern="^(1m|5m|15m|1h|1d|1w|1M)$")
    data: List[StockHistoryPoint]
    
    class Config:
        schema_extra = {
            "example": {
                "symbol": "AAPL",
                "timeframe": "1d",
                "data": [
                    {
                        "timestamp": "2024-01-01T00:00:00Z",
                        "open": 150.0,
                        "high": 155.0,
                        "low": 149.0,
                        "close": 154.0,
                        "volume": 1000000
                    }
                ]
            }
        }


class StockScreeningCriteria(BaseModel):
    """Schema for stock screening criteria."""
    min_price: Optional[float] = Field(None, ge=0, description="Minimum stock price")
    max_price: Optional[float] = Field(None, ge=0, description="Maximum stock price")
    min_volume: Optional[int] = Field(None, ge=0, description="Minimum daily volume")
    min_market_cap: Optional[int] = Field(None, ge=0, description="Minimum market cap")
    max_market_cap: Optional[int] = Field(None, ge=0, description="Maximum market cap")
    sectors: Optional[List[str]] = Field(None, description="Industry sectors to include")
    exchanges: Optional[List[str]] = Field(None, description="Stock exchanges to include")
    
    # Technical indicator criteria
    rsi_min: Optional[float] = Field(None, ge=0, le=100, description="Minimum RSI")
    rsi_max: Optional[float] = Field(None, ge=0, le=100, description="Maximum RSI")
    volume_surge_threshold: Optional[float] = Field(None, ge=1.0, description="Volume surge multiplier")
    price_change_min: Optional[float] = Field(None, description="Minimum price change %")
    price_change_max: Optional[float] = Field(None, description="Maximum price change %")
    
    @validator('max_price')
    def max_price_gte_min_price(cls, v, values):
        if v is not None and 'min_price' in values and values['min_price'] is not None:
            if v < values['min_price']:
                raise ValueError('Max price must be >= min price')
        return v
    
    @validator('max_market_cap')
    def max_market_cap_gte_min_market_cap(cls, v, values):
        if v is not None and 'min_market_cap' in values and values['min_market_cap'] is not None:
            if v < values['min_market_cap']:
                raise ValueError('Max market cap must be >= min market cap')
        return v
    
    @validator('rsi_max')
    def rsi_max_gte_rsi_min(cls, v, values):
        if v is not None and 'rsi_min' in values and values['rsi_min'] is not None:
            if v < values['rsi_min']:
                raise ValueError('RSI max must be >= RSI min')
        return v


class StockScreeningMatch(BaseModel):
    """Schema for individual stock screening match with technical analysis."""
    stock: StockResponse
    technical_score: float = Field(..., ge=0, le=100, description="Technical analysis score (0-100)")
    signals: List[str] = Field(..., description="Active technical signals")
    entry_recommendation: Optional[str] = Field(None, description="Entry recommendation")
    confidence: float = Field(..., ge=0, le=1, description="Overall confidence level")

    # Key technical indicators
    rsi: Optional[float] = Field(None, description="Current RSI value")
    macd_signal: Optional[str] = Field(None, description="MACD signal (bullish/bearish)")
    volume_surge: Optional[bool] = Field(None, description="Volume surge detected")
    price_momentum: Optional[str] = Field(None, description="Price momentum (up/down/neutral)")


class StockScreeningResult(BaseModel):
    """Schema for stock screening results."""
    criteria: StockScreeningCriteria
    results: List[StockScreeningMatch]
    total_matches: int
    execution_time: float = Field(..., ge=0, description="Execution time in seconds")
    timestamp: datetime

    # Summary statistics
    avg_technical_score: float = Field(..., ge=0, le=100, description="Average technical score")
    top_signals: List[str] = Field(..., description="Most common signals in results")
    screening_summary: Dict[str, Any] = Field(..., description="Additional screening insights")


class StockSearchResult(BaseModel):
    """Schema for stock search results."""
    symbol: str
    name: str
    exchange: str
    type: str = Field(..., description="Security type (e.g., 'Equity', 'ETF')")
    currency: Optional[str] = Field(None, description="Trading currency")

    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "name": "Apple Inc.",
                "exchange": "NASDAQ",
                "type": "Equity",
                "currency": "USD"
            }
        }


class EntrySignal(BaseModel):
    """Schema for individual entry signal."""
    detected: bool = Field(..., description="Whether the signal was detected")
    confidence: float = Field(..., ge=0, le=1, description="Confidence level (0-1)")
    description: Optional[str] = Field(None, description="Human-readable description of the signal")

    # Signal-specific data
    current_rsi: Optional[float] = Field(None, description="Current RSI value")
    resistance_level: Optional[float] = Field(None, description="Resistance level for breakout")
    support_level: Optional[float] = Field(None, description="Support level for bounce")
    volume_ratio: Optional[float] = Field(None, description="Volume ratio for surge detection")
    sma_20: Optional[float] = Field(None, description="20-day SMA value")
    sma_50: Optional[float] = Field(None, description="50-day SMA value")
    current_width: Optional[float] = Field(None, description="Current Bollinger Band width")
    avg_width: Optional[float] = Field(None, description="Average Bollinger Band width")
    recent_low: Optional[float] = Field(None, description="Recent low price")


class EntryRecommendation(BaseModel):
    """Schema for entry recommendation."""
    action: str = Field(..., description="Recommended action", pattern="^(STRONG_BUY|BUY|WATCH|HOLD)$")
    reason: str = Field(..., description="Reason for the recommendation")
    entry_score: float = Field(..., ge=0, le=1, description="Overall entry score (0-1)")
    active_signals: List[str] = Field(..., description="List of active signal names")
    signal_count: int = Field(..., ge=0, description="Number of active signals")


class EntryPointAnalysis(BaseModel):
    """Schema for entry point analysis response."""
    symbol: str = Field(..., description="Stock symbol")
    current_price: float = Field(..., gt=0, description="Current stock price")
    analysis_date: datetime = Field(..., description="Analysis timestamp")

    # Entry signals
    entry_signals: Dict[str, EntrySignal] = Field(..., description="Individual entry signals")
    entry_score: float = Field(..., ge=0, le=1, description="Overall entry score")
    recommendation: EntryRecommendation = Field(..., description="Entry recommendation")

    # Technical indicators summary
    indicators: Dict[str, Any] = Field(..., description="Technical indicators used in analysis")

    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "current_price": 175.43,
                "analysis_date": "2024-01-15T10:30:00Z",
                "entry_signals": {
                    "oversold_bounce": {
                        "detected": True,
                        "confidence": 0.7,
                        "description": "RSI oversold and starting to recover",
                        "current_rsi": 28.5
                    }
                },
                "entry_score": 0.65,
                "recommendation": {
                    "action": "BUY",
                    "reason": "Good entry signals present",
                    "entry_score": 0.65,
                    "active_signals": ["oversold_bounce"],
                    "signal_count": 1
                },
                "indicators": {
                    "rsi": 28.5,
                    "macd": {"macd": -0.5, "signal": -0.3, "histogram": -0.2}
                }
            }
        }


class TechnicalIndicatorsResponse(BaseModel):
    """Schema for technical indicators response."""
    symbol: str = Field(..., description="Stock symbol")
    timestamp: Optional[datetime] = Field(None, description="Last update timestamp")
    indicators: Dict[str, Any] = Field(..., description="Technical indicators data")

    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "timestamp": "2024-01-15T10:30:00Z",
                "indicators": {
                    "rsi": 45.2,
                    "macd": {"macd": 0.5, "signal": 0.3, "histogram": 0.2},
                    "bollinger_bands": {"upper": 180.5, "middle": 175.0, "lower": 169.5},
                    "sma_20": 174.8,
                    "sma_50": 172.1
                }
            }
        }


class TradingSignalsResponse(BaseModel):
    """Schema for trading signals response."""
    symbol: str = Field(..., description="Stock symbol")
    timestamp: Optional[datetime] = Field(None, description="Last update timestamp")
    signals: Dict[str, Any] = Field(..., description="Trading signals data")

    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "AAPL",
                "timestamp": "2024-01-15T10:30:00Z",
                "signals": {
                    "rsi_oversold": False,
                    "rsi_overbought": False,
                    "macd_bullish": True,
                    "volume_surge": False,
                    "price_breakout": True,
                    "support_bounce": False
                }
            }
        }


class SpecializedScreeningResponse(BaseModel):
    """Schema for specialized screening responses (momentum, oversold, breakout)."""
    criteria: Dict[str, Any] = Field(..., description="Screening criteria used")
    results: List[StockResponse] = Field(..., description="Matching stocks")
    total_matches: int = Field(..., ge=0, description="Total number of matches")
    timestamp: datetime = Field(..., description="Response timestamp")

    class Config:
        json_schema_extra = {
            "example": {
                "criteria": {"min_price_change": 5.0, "limit": 50},
                "results": [],
                "total_matches": 0,
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }
