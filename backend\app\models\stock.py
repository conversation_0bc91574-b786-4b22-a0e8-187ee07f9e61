"""
Stock-related models for market data and user interactions.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class Stock(Base):
    """Stock model for storing stock information and market data."""
    
    __tablename__ = "stocks"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(10), unique=True, index=True, nullable=False)
    name = Column(String(255), nullable=False)
    exchange = Column(String(10), nullable=False)  # NYSE, NASDAQ, etc.
    sector = Column(String(50), nullable=True)
    industry = Column(String(100), nullable=True)
    market_cap = Column(Float, nullable=True)
    
    # Current market data
    current_price = Column(Float, nullable=True)
    previous_close = Column(Float, nullable=True)
    open_price = Column(Float, nullable=True)
    day_high = Column(Float, nullable=True)
    day_low = Column(Float, nullable=True)
    volume = Column(Integer, nullable=True)
    avg_volume = Column(Integer, nullable=True)
    
    # Price changes
    price_change = Column(Float, nullable=True)
    price_change_percent = Column(Float, nullable=True)
    
    # Technical indicators (stored as JSON for flexibility)
    technical_indicators = Column(JSON, nullable=True)
    
    # Fundamental data
    pe_ratio = Column(Float, nullable=True)
    eps = Column(Float, nullable=True)
    dividend_yield = Column(Float, nullable=True)
    beta = Column(Float, nullable=True)
    
    # Status flags
    is_active = Column(Boolean, default=True)
    is_tradable = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_data_update = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    watchlist_items = relationship("WatchlistItem", back_populates="stock", lazy="dynamic")
    alerts = relationship("Alert", back_populates="stock", lazy="dynamic")
    user_stocks = relationship("UserStock", back_populates="stock", lazy="dynamic")
    price_history = relationship("StockPriceHistory", back_populates="stock", cascade="all, delete-orphan", lazy="dynamic")
    
    def __repr__(self):
        return f"<Stock(symbol='{self.symbol}', name='{self.name}', price={self.current_price})>"
    
    def to_dict(self):
        """Convert stock to dictionary."""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "name": self.name,
            "exchange": self.exchange,
            "sector": self.sector,
            "industry": self.industry,
            "market_cap": self.market_cap,
            "current_price": self.current_price,
            "previous_close": self.previous_close,
            "open_price": self.open_price,
            "day_high": self.day_high,
            "day_low": self.day_low,
            "volume": self.volume,
            "avg_volume": self.avg_volume,
            "price_change": self.price_change,
            "price_change_percent": self.price_change_percent,
            "technical_indicators": self.technical_indicators,
            "pe_ratio": self.pe_ratio,
            "eps": self.eps,
            "dividend_yield": self.dividend_yield,
            "beta": self.beta,
            "is_active": self.is_active,
            "is_tradable": self.is_tradable,
            "last_data_update": self.last_data_update.isoformat() if self.last_data_update else None,
        }


class StockPriceHistory(Base):
    """Historical price data for stocks."""
    
    __tablename__ = "stock_price_history"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False)
    
    # Price data
    open_price = Column(Float, nullable=False)
    high_price = Column(Float, nullable=False)
    low_price = Column(Float, nullable=False)
    close_price = Column(Float, nullable=False)
    volume = Column(Integer, nullable=False)
    adjusted_close = Column(Float, nullable=True)
    
    # Time information
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False, default="1d")  # 1m, 5m, 1h, 1d, etc.
    
    # Relationships
    stock = relationship("Stock", back_populates="price_history")
    
    def __repr__(self):
        return f"<StockPriceHistory(stock_id={self.stock_id}, date={self.date}, close={self.close_price})>"


class UserStock(Base):
    """User-specific stock data and preferences."""
    
    __tablename__ = "user_stocks"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False)
    
    # User preferences for this stock
    notes = Column(Text, nullable=True)
    target_price = Column(Float, nullable=True)
    stop_loss = Column(Float, nullable=True)
    
    # Tracking information
    is_favorite = Column(Boolean, default=False)
    notification_enabled = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="user_stocks")
    stock = relationship("Stock", back_populates="user_stocks")
    
    def __repr__(self):
        return f"<UserStock(user_id={self.user_id}, stock_id={self.stock_id})>"
